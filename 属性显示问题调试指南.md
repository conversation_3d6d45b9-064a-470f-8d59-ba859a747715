# 属性显示问题调试指南

## 🐛 当前问题

用户反馈：**属性添加成功但是没在提升里面显示**

## 🔍 可能的原因分析

### 1. 属性存储问题
- 属性没有正确保存到 `equipSave.obj` 中
- `obj` 的 setter/getter 机制可能有问题

### 2. 属性显示问题
- `EquipPropertyDataCreator` 的扩展属性显示方法有问题
- `getTrueObj()` 方法没有正确返回扩展属性

### 3. 数据同步问题
- 装备编辑后数据没有正确同步到显示系统
- 需要刷新装备显示

## 🔧 调试步骤

### 第一步：测试基础功能

1. **进入装备编辑器**
2. **输入 `99`** - 使用新添加的测试功能
3. **查看弹出的调试信息**，确认：
   - `obj` 是否包含添加的属性
   - `trueObj` 是否包含添加的属性
   - 属性值是否正确

### 第二步：检查属性保存

如果测试显示属性没有正确保存：

#### 问题可能在 `EquipSave.obj` 的 setter 方法：

```actionscript
public function set obj(v0:Object) : void
{
   this._obj = ClassProperty.copyObj(v0);  // 这里可能有问题
}

public function get obj() : Object
{
   return ClassProperty.copyObj(this._obj);  // 这里也可能有问题
}
```

#### 解决方案：
直接操作 `_obj` 而不是通过 setter：

```actionscript
// 修改前
s0.obj["demStroneDropNum"] = 999;

// 修改后
if(!s0._obj) s0._obj = {};
s0._obj["demStroneDropNum"] = 999;
```

### 第三步：检查属性显示

如果属性保存正确但不显示：

#### 检查 `EquipPropertyDataCreator.getSpecialDropText()` 方法

确认方法中的属性检查逻辑：

```actionscript
// 检查这些条件是否正确
if(obj0["demStroneDropNum"]) str0 += "\n<yellow 无双水晶掉落 +" + obj0["demStroneDropNum"] + "个/>";
```

可能需要改为：

```actionscript
// 更严格的检查
if(obj0.hasOwnProperty("demStroneDropNum") && obj0["demStroneDropNum"] > 0) 
   str0 += "\n<yellow 无双水晶掉落 +" + obj0["demStroneDropNum"] + "个/>";
```

### 第四步：检查 `getTrueObj()` 方法

`getTrueObj()` 方法可能在强化或进化处理中丢失了扩展属性：

```actionscript
public function getTrueObj() : Object
{
   var obj0:Object = this.obj;  // 这里获取的obj是否包含扩展属性？
   if(this.strengthenLv > 0)
   {
      obj0 = EquipStrengthenCtrl.getNewObj(this);  // 强化处理是否保留扩展属性？
   }
   if(this.getDefine().isCanEvoB())
   {
      obj0 = EquipEvoCtrl.getNewObj(this.getDefine(),obj0,this.evoLv);  // 进化处理是否保留扩展属性？
   }
   return obj0;
}
```

## 🛠️ 修复方案

### 方案1：直接操作 `_obj`

修改装备编辑器中的属性设置方式：

```actionscript
// 在 addSuperPropertiesPackage 方法中
private function addSuperPropertiesPackage(s0:EquipSave) : void
{
   // 直接操作内部对象
   if(!s0._obj) s0._obj = {};
   
   s0._obj["demStroneDropNum"] = 999;
   s0._obj["demBallDropNum"] = 999;
   // ... 其他属性
   
   Gaming.uiGroup.alertBox.showSuccess("已添加超级属性包！");
}
```

### 方案2：修复 `getTrueObj()` 方法

确保 `getTrueObj()` 方法保留所有扩展属性：

```actionscript
public function getTrueObj() : Object
{
   var obj0:Object = ClassProperty.copyObj(this.obj);  // 深拷贝确保不丢失属性
   
   if(this.strengthenLv > 0)
   {
      var strengthenObj:Object = EquipStrengthenCtrl.getNewObj(this);
      // 合并属性而不是替换
      for(var prop:String in strengthenObj)
      {
         obj0[prop] = strengthenObj[prop];
      }
   }
   
   if(this.getDefine().isCanEvoB())
   {
      var evoObj:Object = EquipEvoCtrl.getNewObj(this.getDefine(),obj0,this.evoLv);
      // 合并属性而不是替换
      for(var prop2:String in evoObj)
      {
         obj0[prop2] = evoObj[prop2];
      }
   }
   
   return obj0;
}
```

### 方案3：强制刷新显示

在属性编辑完成后强制刷新装备显示：

```actionscript
// 在属性编辑完成后
this.nowData.save = s0;
this.showOneEquipDataAndPan(this.nowData);

// 强制刷新背包显示
Gaming.uiGroup.allBagUI.fleshAllBox();

// 如果装备已穿戴，刷新角色属性
if(this.nowData.placeType == "wear")
{
   this.nowData.normalPlayerData.fleshAllByEquip();
}
```

## 📋 测试清单

### ✅ 测试步骤

1. **[ ]** 使用 `99` 测试基础属性保存
2. **[ ]** 检查 `obj` 和 `trueObj` 内容
3. **[ ]** 确认属性值正确设置
4. **[ ]** 验证属性显示文本生成
5. **[ ]** 测试装备提升信息显示

### 🎯 预期结果

测试成功后，装备的"提升"部分应该显示：

```
提升：
战斗力/神级 16%
伤害 74%
生命 74%
技能冷却速度 27%
基础伤害 999           ← 测试属性
生命值 999             ← 测试属性
无双水晶掉落 +999个     ← 扩展属性
幸运值 999             ← 扩展属性
```

## 🚀 下一步行动

1. **立即测试**：使用 `99` 功能进行调试
2. **根据结果**：选择对应的修复方案
3. **验证修复**：确认属性正确显示
4. **完善功能**：移除调试代码，完善用户体验

通过这个系统性的调试过程，我们应该能够找到并解决属性不显示的根本问题！
