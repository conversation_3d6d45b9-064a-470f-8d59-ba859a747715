===============================================
           游戏完整技能列表
===============================================

本文档包含游戏中所有可获得的技能，按类型分类整理。
所有技能均为对玩家有利的正面效果。

===============================================
⚔️ 武器技能 (armsSkillClass.bin)
===============================================

🔥 爆炸伤害类：
• 爆胆 - 增加爆炸伤害
• 爆胆-无法叠加得buff - 爆炸效果限制版
• 爆沙 - 沙土爆炸
• 爆石 - 石块爆炸  
• 爆岩 - 岩石爆炸
• 爆震 - 震动爆炸

🗡️ 近战攻击类：
• 背刺 - 背后攻击加成
• 搏命 - 拼命攻击
• 跳斩 - 跳跃攻击
• 痛击 - 重击伤害
• 突袭 - 突然攻击

🎯 射击增强类：
• 超级飞镰-破甲 - 强化飞镰破甲
• 超级共振 - 强化共振效果
• 超级派生 - 强化派生子弹
• 超级粘性 - 强化粘性效果
• 穿刺 - 穿透攻击
• 弹力回收 - 子弹回收
• 飞镰 - 飞行攻击
• 跟踪飞镰 - 追踪攻击
• 共振 - 共鸣效果
• 贯穿波 - 贯穿攻击
• 击中派生 - 击中产生派生
• 击中闪电 - 击中产生闪电
• 击中溅射 - 击中溅射伤害
• 连弩 - 连续射击
• 裂空波 - 空间攻击
• 远射 - 远程攻击
• 震动炮 - 震动攻击
• 子母弹 - 分裂子弹

🔄 恢复增益类：
• 击毙补充弹药 - 击杀回弹药
• 击毙回复 - 击杀回血
• 击中回复 - 击中回血
• 刷新 - 技能刷新
• 振奋 - 状态恢复

🎮 控制效果类：
• 超级减速 - 强力减速敌人
• 超级麻痹 - 强力麻痹敌人
• 击中沉默 - 击中沉默敌人
• 击中减速 - 击中减速敌人
• 击中麻痹 - 击中麻痹敌人
• 击中眩晕 - 击中眩晕敌人
• 致残 - 削弱敌人攻击力
• 致盲 - 致盲敌人

🌟 特殊效果类：
• 超重 - 重量效果
• 陈年老糖 - 特殊增益
• 惩罚 - 惩罚效果
• 打滑 - 滑动效果
• 风雷 - 风雷攻击
• 轨迹编辑 - 轨迹控制
• 火焰始祖 - 火焰伤害
• 击毙溅射 - 击杀溅射
• 静电反应 - 静电效果
• 剧毒 - 毒素伤害
• 绝灭 - 绝灭效果
• 快感 - 快感效果
• 溃甲 - 破甲效果
• 老练 - 老练加成
• 冷凝 - 冷凝效果
• 迷失电流 - 电流攻击
• 破甲 - 破除护甲
• 七步毒 - 毒素效果
• 上帝之杖 - 神级武器
• 盛血绞杀 - 血腥攻击
• 蚀骨 - 腐蚀效果
• 瞬秒 - 瞬间击杀
• 无限火力 - 无限射击
• 膝跳反应 - 反应攻击
• 烟花 - 烟花效果
• 炎爆 - 火焰爆炸
• 炎爆-爆 - 火焰爆炸触发
• 引爆 - 引爆效果
• 影灭 - 影子攻击
• 粘性 - 粘性效果
• 战修罗 - 战斗狂暴

===============================================
🏛️ 联盟技能 (unionSkillClass.bin)
===============================================

🎯 猎手系列：
• 怪物猎人 - 对普通怪物伤害加成
• 精英克星 - 对精英怪物伤害加成
• 恶魔猎手 - 对恶魔伤害加成
• 虚幻猎手 - 对虚幻单位伤害加成
• 人类克星 - 对人类单位伤害加成

🌟 神级技能：
• 上帝之眼 - 神级攻击技能
• 上帝之眼-防御力降低 - 降低敌人防御
• 上帝之杖 - 神级武器技能
• 上帝之杖-防御力降低 - 降低敌人防御

🏃 移动技能：
• 鬼步 - 高速移动能力

🐾 召唤技能：
• 暴力尸宠 - 召唤暴力宠物
• 恶魔屠刀 - 恶魔武器

===============================================
⭐ 90级技能 (ninetySkillClass.bin)
===============================================

❄️ 冰雪系列：
• 飞雪连舞 - 连续冰雪攻击
• 钩雪莲华 - 钩拉冰雪攻击
• 钩雪莲华-击中 - 钩拉击中效果
• 钩雪莲华-抛空中 - 钩拉抛空效果
• 雪藏 - 雪中隐藏
• 雪引冲锋 - 雪中冲锋
• 雪引冲锋-冲撞 - 冲锋冲撞效果
• 召唤雪人 - 召唤雪人助手

⚡ 战斗技能：
• 爆怒一击 - 愤怒重击
• 分身 - 创造分身
• 风暴突袭 - 风暴攻击
• 钩拉 - 钩拉敌人
• 钩拉-击中麻痹 - 钩拉麻痹效果
• 踢爆 - 踢击爆炸
• 踢爆-冲撞 - 踢击冲撞

🛡️ 防御技能：
• 近战防御 - 近战防护
• 摩卡护体 - 护体效果
• 千斤顶 - 重量防护

🌟 特殊效果：
• 白色血液效果 - 白色血液
• 绿色血液效果 - 绿色血液
• 腐蚀 - 腐蚀效果
• 击中沉默 - 击中沉默
• 飓风载体 - 飓风效果
• 收到攻击打断风暴突袭 - 攻击打断
• 诱击对玩家单位伤害降低 - 伤害减免

===============================================
🛡️ 装备技能 (equipSkillClass.bin)
===============================================

🔮 力场防护：
• 磁力场 - 磁力防护
• 磁力场-曲扭光环 - 磁力光环
• 负离子外壳 - 离子防护
• 净化器 - 净化效果
• 折射 - 攻击折射

🛡️ 护甲防御：
• 钢背 - 背部装甲
• 荆棘外表 - 反伤护甲
• 芒刺 - 尖刺防护
• 免疫 - 状态免疫
• 装甲压制 - 装甲效果

🌟 光环效果：
• 耐久光环 - 持久防御
• 顽强光环 - 坚韧防御
• 上帝的护佑 - 神圣保护

🐺 召唤技能：
• 召唤群狼 - 召唤狼群
• 嗜爪之怒 - 爪击狂暴

💀 特殊效果：
• 牺牲 - 牺牲效果
• 瘴气 - 毒气效果

===============================================
🐾 宠物技能 (petSkillClass.bin)
===============================================

🌟 光环系列：
• 防御光环 - 增加防御力
• 复原光环 - 生命回复
• 敏捷光环 - 射速提升
• 强击光环 - 攻击力提升
• 减速光环 - 减速敌人
• 退化光环 - 降低敌人防御
• 致残光环 - 削弱敌人攻击
• 致盲光环 - 致盲敌人

⚡ 攻击技能：
• 爆石 - 石块爆炸
• 大地之怒 - 大地攻击
• 定点轰炸 - 精确轰炸
• 极速炮轰 - 高速炮击
• 聚能电流 - 电流攻击
• 聚能力量 - 力量聚集
• 狂刃追踪 - 追踪攻击
• 旋风刀 - 旋风攻击
• 无尽轰炸 - 持续轰炸
• 灼热射线 - 射线攻击
• 灼热射线-附带技能 - 射线附带效果

🛡️ 防护技能：
• 补给头盔 - 补给防护
• 先锋盾 - 先锋防护
• 上帝的护佑 - 神圣保护

🎮 控制技能：
• 沉默 - 沉默敌人
• 减速 - 减速敌人
• 闪电麻痹 - 闪电麻痹
• 眩晕 - 眩晕敌人
• 致盲 - 致盲敌人

🌟 增益技能：
• 充能 - 能量充能
• 弹性世界 - 弹性效果
• 电离反转 - 电离效果
• 电离折射 - 电离折射
• 反击 - 反击攻击
• 辐射光球 - 辐射攻击
• 飓风 - 飓风效果
• 狂暴 - 狂暴状态
• 馈赠 - 馈赠效果
• 欺凌 - 欺凌效果
• 全域圣光 - 全场治疗
• 群体圣光 - 群体治疗
• 闪烁 - 瞬移能力
• 闪烁-目标点爆炸 - 瞬移爆炸
• 顽强 - 坚韧效果
• 巫尸之怒 - 巫术愤怒
• 牺牲 - 牺牲效果
• 野性召唤 - 野性召唤
• 远视 - 远程视野
• 震地 - 震地攻击
• 自燃 - 自燃效果

🐺 图腾召唤：
• 狼图腾 - 狼族图腾

🌫️ 特殊效果：
• 毒雾 - 毒雾攻击
• 静电过载 - 静电效果

===============================================
🚗 载具技能 (vehicleSkillClass.bin)
===============================================

⚡ 聚合系列：
• 霸空聚合 - 空中聚合
• 轰天聚合 - 天空聚合
• 镇山聚合 - 山地聚合

🛡️ 防护系统：
• 合金外壳 - 装甲防护
• 守望之盾 - 防护盾牌

💥 攻击系统：
• 冲击波 - 冲击攻击
• 核弹头 - 核武攻击
• 见面礼 - 初始攻击

🔋 动力系统：
• 核动力 - 核能驱动

🎯 特殊能力：
• 降低攻击力 - 削弱敌人
• 特效 - 特殊效果
• 遇强则强 - 适应能力

===============================================
⚔️ 兵器技能 (weaponSkillClass.bin)
===============================================

⚡ 电离系列：
• 电离驱散 - 电离攻击
• 电离驱散-任何 - 全能电离

🔧 特殊效果：
• 崩溃 - 崩溃效果
• 加持 - 加持效果
• 凯撒特效附带 - 凯撒效果

===============================================
👕 时装技能 (outfitSkillClass.bin)
===============================================

🥷 隐身系列：
• 遁形 - 隐身能力
• 沃龙隐 - 沃龙隐身

🩸 血液系列：
• 坏血 - 坏血效果
• 血脉沸腾 - 血脉激活

🦅 感知系列：
• 鹰眼 - 精准射击
• 饥饿行踪 - 追踪能力

🐺 变形系列：
• 猎手原形 - 猎手形态

🏃 移动系列：
• 悦动先锋 - 移动加成

🛡️ 防护系列：
• 战争抗体 - 战斗抗性

===============================================
🔧 零件技能 (partsSkillClass.bin)
===============================================

💥 攻击效果：
• 爆裂 - 爆裂攻击
• 猎击 - 猎杀攻击
• 酸性腐蚀 - 酸性伤害

🔋 补给效果：
• 弹药补充 - 弹药回复

🍰 特殊道具：
• 吃月饼 - 月饼效果

===============================================
🎯 通用技能 (skillClass.bin)
===============================================

💊 药剂道具：
• 无敌药水 - 短暂无敌
• 无敌罐头 - 无敌状态
• 增加血量 - 血量提升
• 增加弹药 - 弹药补充
• 增加移动速度 - 移速提升

🍰 食物道具：
• 月饼增加攻击力 - 攻击力+100%
• 汤圆增加攻击力 - 攻击力+100%
• 九层糕增加射速 - 射速+100%
• 饺子无敌状态 - 无敌效果

🎴 卡片技能：
• 超级散射卡 - 散射增强
• 骷髅卡 - 骷髅效果
• 神车卡片 - 载具增强
• 神宠卡片 - 宠物增强
• 神队卡片 - 团队增强

🥷 隐身技能：
• 无敌隐身 - 隐身无敌
• 无敌隐身-重生石 - 重生保护

⚡ 特殊增益：
• 无敌之怒 - 对无敌怪物伤害+400%
• 无限馈赠 - 射速+5倍
• 飞雷神 - 队友防御+30%
• 鼓舞士气 - 士气提升
• 队长之魄 - 队长加成

🎃 节日技能：
• 南瓜头技能 - 载具后攻击+100%射速+100%
• 小炎戒 - 特殊戒指效果

🔄 掉落增益：
• 双倍黑色武器掉落 - 武器掉落翻倍
• 双倍黑色装备掉落 - 装备掉落翻倍
• 双倍黑色武器掉落和30%宝石掉落 - 综合掉落
• 双倍黑色装备掉落和30%宝石掉落 - 综合掉落

🤖 机械系列：
• 挖掘者-超能推撞 - 超能攻击
• 挖掘者-动能推撞 - 动能攻击
• 挖掘者-动能推撞-推 - 推撞效果

🎮 游戏角色：
• 鸣人 - 火影角色技能
• 恶魔风脚 - 恶魔技能
• 翻滚 - 翻滚能力
• 英雄跳斩-降低目标攻击力 - 跳斩削弱

🔮 召唤技能：
• 召唤摩卡 - 召唤摩卡助手

🌟 磁力技能：
• 磁力场 - 磁力防护

===============================================
📊 技能统计总结
===============================================

🎯 攻击增强类技能: 约120个
   - 直接伤害提升
   - 特殊攻击效果
   - 元素伤害加成

🛡️ 防御保护类技能: 约60个
   - 无敌和护盾效果
   - 伤害减免
   - 状态免疫

🎮 控制辅助类技能: 约45个
   - 敌人控制效果
   - 团队光环增益
   - 召唤和辅助

🔄 恢复补给类技能: 约20个
   - 生命回复
   - 弹药补充
   - 状态恢复

===============================================
💡 使用说明
===============================================

1. 所有技能均为正面效果，对玩家有利
2. 技能可通过以下方式获得：
   - 武器自带技能
   - 装备强化获得
   - 联盟建筑解锁
   - 等级提升奖励
   - 稀有属性随机
   - 特殊道具使用

3. 部分技能可以叠加，效果会累积
4. 神级技能通常效果更强，获得难度更高
5. 光环类技能影响范围内的友方单位

===============================================
🎉 总计: 约245个有益技能
===============================================

祝你游戏愉快！愿你的装备技能越来越强大！

文档生成时间: 2025年1月
版本: 完整版 v1.0




  var allSkills:Array = this.getAllSkills();
         var ArrColor0:* = ["earthquake","earthquake_link","endlessRocket","lightningTower","energyShield","electricDevicer","electricDevicerClearKing","lightningTower_die","lightningTower_lightning","shuttleDevicer","blackHoleDevicer","dinosaurEgg","squibDevice","terroristBox","knightsMedal","skeletonMedal","skeletonWand","skeletonWandHit","terroristBox_screaming","squibDevice_screaming","outfit_follow","outfit_blood","outfit_shootMissile","outfit_jump","outfit_crit","outfit_boom","outfit_wolong","outfit_wolong_hurt","outfit_eagle","outfit_elephant","nuclear_peak","gaze_peak","sacrifice_peak","invisibilityEver","upperLimitSecond","upperLimitSecond2","noSkillHurt","noBulletHurt","onlyWeaponHurt","onlyWeaponHurtBattle","onlyWeaponHurtSet","onlyUnderCrossbow","onlyUnderOldRocket","State_SpellImmunity","State_Invincible","State_InvincibleThrough","State_noAiFind","State_noAllSkill","noAttackOrder","noMoveSpeed","noArmsShoot","killMeTimeOver","aiExcape","sumBossAtten","sumBossAtten2","sumBossAtten3"
         ,"offBossAtten","metalCorrosion","State_lowMove","State_AddMove","State_AddMove50","jumpNumAdd1","jumpNumAddZero","standImageByLife","UnderRos_AddMove_Battle","boom_headless","suicide_headless","cmldef_enemy","cmldef2_enemy","cmldef3_enemy","Hit_Crit_Fat","circle_inward_shell","sweep_shell","Doctor2_cloned","FightKing_disabled","crazy_king","hyperopia_incapable","through_enemy","noBulletReduct","throughClose","sweep_runAway","selfBurn_task","hammer_hit","choppedAttack_FightShooter","windAttack_FightShooter","FightKing_hitParalysis","FightKing_crazy","SmallSpider_hitParalysis","suckBlood_enemy","gaze_enemy","despise_enemy","fastForward_enemy","hurtDefence_enemy","suicide_treasure","rigidBody_enemy","defenceBounce_enemy","noBounce_enemy","roll_hugePosion","posion7_hugePosion","corrosion_hugePosion","silence_hugePosion","hiding_hugePosion","splash_HugePoison","summonedGasBom_HugePoison","suicide_GasBomb","selfBoom_GasBomb","slowMove_GasBomb","noUnderHurt5_GasBomb","killCharm","screaming_enemy"
         ,"changeToZombie_enemy","skillCopy_enemy","invisibility_enemy","knife_skeleton","knife_skeleton2","atry_skeleton","blindness_skeleton","teleport_skeleton","crazy_skeleton","hammer_enemy","hammer_enemy_link","knife_skeleton_hammer","ironBody_enemy","magneticField_enemy","magneticField_enemy_link","beatBack_iron","silence_wind","emp_wind","slowMove_wind","bar_wind","wizardAnger_wind","wizardAnger_wind_blood","poisonClaw_wind","noSR","xxx_wind","winding_mummy","noSpeedReduce","crazy_knights","hammer_knights","treater_knights","trample_knights","boundless_enemy","boundless_enemy_link","boundless_enemy_pass","groupCrazy_enemy","lifeLink_wolf","feeding_wolf","groupReverseHurt_enemy","posion7_wolf","summon_FightWolf","anger_FightWolf","hammer_FightWolf","laser_FightWolf","treater_FightWolf","NuggetsShoot","wind_Nuggets","likeMissle_Shapers","likeMissleNo","likeMissleNo2","missile_Sentry","addMove_Crawler","summonedSpider_Crawler","Crawler_cloned","crazy_enemy","groupLight_enemy","groupSpeedUp_enemy"
         ,"teleport_enemy","hiding_enemy","hidingAll_enemy","feedback_enemy","imploding_enemy","paralysis_enemy","pointBoom_enemy","sweep_enemy","moreMissile_enemy","liveReplace_enemy","silence_enemy","globalSpurting_enemy","murderous_enemy","poisonClaw_enemy","selfBurn_enemy","bullying_enemy","skillGift_enemy","disabled_enemy","slowMove_enemy","strong_enemy","slowMoveHalo_enemy","disabledHalo_enemy","trueshot_enemy","corrosion_enemy","desertedHalo_enemy","rebirth_enemy","recovery_enemy","recoveryHalo_enemy","tenacious_enemy","reverseHurt_enemy","teleport_enemy15","backHurt_enemy_link","paralysis_enemy_link","lifeReplace_enemy_link","skillCopy_enemy_link","globalSpurting_enemy_link","slowMoveNoClear","onlyUnderMiningSpade","onlyUnderMiningShovels","onlyUnderChristmasGun","noEleUnder","lifeBarShowCd","orcCreateThings","dedicationLove","resistPerLove2","defenceAddLove","vehicleAddLove30","resistPerLoveAdd2","underInvincibleHurtLove","nearAddLifeLove","mainResistPerLove","summonedSpider_spiderKing"
         ,"feeding_spider","pounce_spider","electricBoom_enemy","invisibility_SpiderKing","acidRain_SpiderKing","summonedSpider_spiderKing_extra","DropEffect_AddLifeMul","DropEffect_AddChargerMul","invincibleDrugDrop","DropEffect_AddMoveSpeedMul","addMocha","godHiding_things","godHiding_Pet","moonCake","tangyuan","jiaozi","armsDropDouble","equipDropDouble","armsDropDoubleAndGem","equipDropDoubleAndEquipGem","highPetCard","highVehicleCard","highCardState","electromagnet","superSpreadCard","skeletonCard","skeletonCard_link","pumpkinHead","pumpkinHead","wolfFashionSkill","goldFalcon","dragonHeadSkill","crazy_sanji","xiaoBoShoot","xiaoMingShoot","seckillNormalEnemy","xiaoAiShoot","shotgunBladeHero","chinaCaptainSkill","armyCommanderSkill","bladeSkill","cyanArmySkill","bioShockSkill","hurtBossAdd","highDrill","superHighDrill","highDrillHit","extendCd","arenaHurtAdd","Hit_SlowMove","Range_SlowMove","Manual_AddLife","BackHurt","pumpkinDropEffect","gmMask1","gmMask2","gmMask3","superMoreBullet","superSkillGift"
         ,"maxSpeedTask","madmanHead","betHit","killAll","attackNoDodge","findHide","invincibleZombieEnemyHit","sniperKingBuff","sniperKingEnemyHit","sniperKingEnemyUnder","sniperKingEnemyUnder2","bulletRainBallHit","flySkyBatBuff","rifleSensitive","sniperSensitive","shotgunSensitive","pistolSensitive","rocketSensitive","crossbowSensitive","flamerSensitive","laserSensitive","otherSensitive","weaponSensitive","vehicleSensitive","petSensitive","redArmsSensitive","handSensitive","wizardAngerEdit","huntParts","acidicParts","whirlwind_FightKing","shake_FightKing","shoot_FightKing","hyperopia_pet","shelling_ZombieKing","helmet_PetZombieFootball","shoot_PetZombieFootball","globalLight_PetZombieFootball","shoot2_PetTyphoonWitch","wind_PetTyphoonWitch","wizardAnger_PetTyphoonWitch","sacrifice_PetIronChief","defenceAuras_PetIronChief","godHand_PetIronChief","endlessBombing_skull","current_skull","degradation_PetBoomSkull","summon_PetFightWolf","laser_PetFightWolf","treater_PetFightWolf","anger_PetFightWolf"
         ,"charged_PetLake","lightBall_PetLake","static_PetLake","agile_PetLake","blindness_anger_PetFightWolf","lightBall_PetLake_slow","dizziness_anger_PetFightWolf","laser_PetFightWolf_extra","flash_pet_link","selfBurn_pet","crazy_pet","silence_pet","groupLight_pet","tenacious_pet","feedback_pet","skillGift_pet","pioneer_pet","groupReverseHurt_pet","poisonousFog_pet","pointBoom_pet","bullying_pet","flash_pet","gngerFire_pet","paralysis_pet","imploding_pet","strong_pet","trueshot_pet","recoveryHalo_pet","disabledHalo_pet","slowMoveHalo_pet","watchmanHurtNormal","watchmanHurtSuper","watchmanHurtHuman","watchmanHurtBoss","superWatchmanHurtBoss","godEyes","godMace","heroSprint","godEyesDefence","godMaceDefence","dodgeProZang","defenceZang","hurtStrikerZang","noHurtZang","sameArmsHurtAddZang","hurtZang","hurtHoleZang","SnowThinRuin","fightReduct2","SnowFattySprint","SnowFattySprintHit","hitBloodWhite","hitBloodGreen","silence_SnowSoldiers","clonedSnowSoldiers","teleport_SnowSoldiers","underCrossbow_SnowSoldiers"
         ,"SnowGirlHook","SnowGirlPull","SnowGirlHookHit","SnowGirlPullHit","SnowGirlAfterPullHit","IceManCorrosion","IceManRotate","IceManStrike","IceManShake","IceManKick","IceManSnowman","IceManRotateLink","IceManStrikeHit","IceManKickHit","snowWind","RifleHornetShooterHurt","escapeInvincible","dodge_loveSkill","girlDefence_loveSkill","girlHurt_loveSkill","lovePower_loveSkill","rebirth_loveSkill","addLifeMul_loveSkill","lifeBottle_loveSkill","element_loveSkill","elementLifeBack_loveSkill","thorns_pig","fleshFeast_pig","thunder_pig","collision_pig","roll_pig","godHand_equip","immune_equip","magneticField_equip","strongHalo_equip","murderous_equip","poisonRange_equip","attackSpeedHalo_equip","sacrifice_equip","backStrong_equip","anionSkin_equip","treater_equip","backWeak_equip","thornSkin_equip","refraction_equip","summonWolf_bigBoss","zoomOut","magneticField_enemy_link","refraction_equip_link","tauntLing","strongLing","revengeLing","resonanceLing","nightLing","clawLing","tauntLing_link","tauntLingBack_link"
         ,"revengeLing_link","Hit_SlowMove_ArmsSkill","Kill_AddCharger_ArmsSkill","Kill_AddLifeMul_ArmsSkill","Hit_Poison_ArmsSkill","Kill_Spurting_ArmsSkill","Kill_Crazy_ArmsSkill","Hit_Spurting_ArmsSkill","Hit_disabled_ArmsSkill","Hit_Paralysis_ArmsSkill","Hit_blindness_ArmsSkill","Hit_AddLifeMul_ArmsSkill","Hit_silence_ArmsSkill","Hit_hitMissile_godArmsSkill","Hit_seckill_godArmsSkill","Hit_finalkill_godArmsSkill","Hit_crazy_godArmsSkill","Hit_atry_godArmsSkill","Hit_posion7_godArmsSkill","Hit_imploding_godArmsSkill","Hit_pointBoom_godArmsSkill","Hit_fleshSkill_godArmsSkill","Hit_Lightning_godArmsSkill","Hit_Hammer_godArmsSkill","Hit_SuperMissile_godArmsSkill","Hit_Paralysis_ArmsSkill2","Hit_burn_ArmsSkill","cold_ArmsSkill","erosion_ArmsSkill","beatBack_ArmsSkill","combo_ArmsSkill","viscous_ArmsSkill","viscousSuper_ArmsSkill","flyDragonHead","bangerGunSkill","infiniteFire","shotgunBlade_ArmsSkill","godMace_ArmsSkill","windThunder_ArmsSkill","laserKill_godArmsSkill","imploding_blackArmsSkill","fear_godArmsSkill"
         ,"fear_godArmsSkill2","sickle_godArmsSkill","sickle_godArmsSkill2","sickle_godArmsSkill","sickle_godArmsSkill2","demonAddHurt","noFoggyDef","noFoggyDef","immuneNemesis","lash_ArmsSkill","beadCrossbow_ArmsSkill","editBulletPath","Hit_burn_ArmsSkill_link","fear_noBuff","sickle_godArmsSkill2_link","vehicleFit_Gaia","vehicleFit_Civilian","vehicleFit_fly","crazy_vehicle","murderous_vehicle","blade_blueMoto","WatchEagleAirDefence","alloyShell","strongStrong","shockWave","meetingGift","blade_blueMoto_link","blueMoto_state","redMoto_state","defenceLing","noHurt10Lings","resistPerLing4","hurtStrikerLing","hurtLing40","vehicleLing40","ironBodyLing","eleStrikerLing","dropStrikerLing","FightKing_slowMove","FightKing_cloned","knifeBoom_FightKing","summonedPo_extra","FightKing_cloned_extra","silence_FightKing","coverFog_FightKing","weaponEmp","weaponEmpAll","goldSpadeSkill","goldSpadeSkill","sprintSwordHit","sprintSwordHit_extra","stoneSeaBuff","pioneerDemon","demonShield","demCloned","weaponDefence"
         ,"fitVehicleDefence","immune","offAllSkill","toLand","underToLand","ruleRange","bladeShield","meteoriteRain","lightningFloor","killPet","enemyEmp","invincibleEmp","moreBullet","resistMulHurt","corpsePoison","noUnderFlyHit","noUnderLaser","revengeArrow","revengeGhost","deadlyArrow","deadlyGhost","killAllExcludeVehicle","fightBackBullet","screwBall","shortLivedDisabled","shortLivedDisabledUnderHit","summonShortLife","summonShortLifeMax","enemyToZombie","enemyToZombieDie","enemyToSpider","verShield","verShieldBuff","midLightning","godShield","lockLife","killXinLing","cantMove","noPurgoldArms","everSilenceEnemy","everNoSkillEnemy","firstLivePer10","groupLight_hero","crazy_hero","tenacious_hero","hitMissile_hero","devour_hero","pointBoom_hero","hyperopia_hero","hiding_hero","moreMissile_hero","through_hero","poisonousFog_hero","murderous_hero","bullying_hero","charm_hero","feedback_hero","skillGift_hero","silence_hero","myopia_hero","pioneer_hero","globalSpurting_hero","selfBurn_hero","accurate_hero"
         ,"lookDown_hero","eleOverlap_hero","bloodShield_hero","gliding_hero","rolling_hero","kingWing_hero","possession_hero","silverScreen_hero","invisibility_hero","screaming_hero","groupReverseHurt_hero","globalLight_hero","coquettish_hero","wisdomAnger_hero","poisonousFog_hero_link","devour_hero_link","backHurt_hero_link","selfBurn_hero_link","wisdomAnger_hero_link","rolling_hero_link","silence_hero_link","moreMissile_hero_dizziness","moreMissile_hero_dizziness2","clearGas_WatchEagle","gas_WatchEagle","sprint_WatchEagle","wind_WatchEagle","Triceratops_stone","Triceratops_hard","Triceratops_egg","Triceratops_oasis","Triceratops_deserted","noDegradation","DesertOasis_thirst","FlyDragon_fireSurround","FlyDragonBall","FlyDragon_petrifaction","FlyDragon_dodgePro","FlyDragon_summoned","FlyDragon_likeMissle","fightReduct","FlyDragon_hammer","SaberTiger_laser","SaberTiger_missile","SaberTiger_ChemicalTank","SaberTiger_shield","SaberTiger_rebirth","SaberTiger_shield_first","SaberTiger_shield_second"
         ,"SaberTiger_shield_anger","SaberTiger_shield_defence","TriceratopsHurt","ChemicalTank_Triceratops","Mammoth_die","Mammoth_electricity","Mammoth_missile","Mammoth_missileChip","Mammoth_core","Mammoth_hurt","Mammoth_coreShow","Mammoth_core_die","Mammoth_core_attached","Mammoth_core_blueElec","Mammoth_core_redElec","Mammoth_core_hurt","Mammoth_wolf","Mammoth_wolf_die","invincible_eeg","magneticField_egg","block_egg","trueshot_eeg","underHit_Paralysis_link","VanityKer_comet","VanityKer_cometBuff","VanityKer_rayBuff","VanityKer_dreamland","VanityKer_dreamlandUnit","VanityKer_feeding","VanityKer_antimatter","Antimatter_hammer","Antimatter_die","Nian_change","Nian_change_link","Nian_spark","Nian_dartsParalysis","FireWolf_rockFire","FireWolf_noFire","FireWolf_elements","FireWolf_cloned","FireWolf_noRockFire","elementsYellow","elementsRed","elementsGreen","elementsBlue","elementsPurple","Salamander_wather","Salamander_wather","Salamander_burrow","Salamander_back","Salamander_bubbles","bubbles_hammer"
         ,"bubblesGotoCamp","bubblesDie","Bubbles_blue","Bubbles_green","BubblesJump","VirtualScorpion_press","VirtualScorpion_wind","VirtualScorpion_light","VirtualScorpion_defence","VirtualScorpion_windHurt","DryFrogPull","DryFrogPullHit","DryFrogPullPosion","DryFrogRotate","DryFrogPour","DryFrogJump","eleField_FastGuards","eleField_FastGuards_link","FastGuards_screen","FastGuards_missile","FastGuards_spring","Weaver_smoke","Weaver_web","Weaver_web_hiding","Weaver_web_life","Weaver_summoned","Weaver_thorn","Weaver_thorn_hit","DuelistEffect","DuelistShake","DuelistShakeSlow","DuelistShoot","DuelistCombo","DuelistComboHit","DuelistCloned","DuelistSummoned"];
         var ArrColor1:* = ["地震","地震-使目标防御力降低","无尽轰炮","召唤闪电塔","能量外壳","全域电击","全域电击-清除王者之剑buff","逐渐死亡","闪电","穿梭","黑洞","异龙蛋","爆竹","恐怖盒子","无疆骑士","召唤暴君","骷髅权杖","骷髅权杖-击中","惊吓","惊吓","战争抗体","坏血","猎手原形","悦动先锋","饥饿行踪","血脉沸腾","沃龙隐","沃龙隐","鹰眼","遁形","核爆","注视","搏命","永久隐身","上限","上限","魔抗","远程抵抗","只受副手伤害","只受副手伤害","只受副手伤害，指定伤害","只受弩伤害","只受古老的火炮伤害","技能免疫","永久无敌","永久无敌且不受碰撞","隐藏","自封","自闭","禁止","无法射击","自刎","逃跑","百分比攻击衰减","百分比攻击衰减","百分比攻击衰减","取消百分比伤害","酸性腐蚀","低速","超速","倍速","弹跳次数+1","不能弹跳","根据生命值扣除显示stand状态","易怒","自爆炸弹","无头自爆僵尸-启动攻击后自爆","防化","防毒","抗毒","暴击","圆周弹","导弹召唤","分身","致残","僵尸王-狂暴","远视","永久金刚钻","无限弹药","关闭金刚钻","导弹召唤-逃出升天任务","自燃","击中眩晕","震地","旋风刀","击中麻痹","狂暴","击中麻痹","吸血","凝视","藐视","快进","加攻加防","20秒后自爆","刚体","胶性表皮","空虚","巨力滚","七步毒","蚀毒","沉默","隐身","反溅","毒气弹","毒气弹-靠近自爆","毒气弹-自爆","减速","原始无敌5秒","破魅","尖叫","尸化","技能复制","隐匿之雾","地狱之刃","地狱之刃","最后一搏","致盲","瞬移","狂暴","眩晕之锤","眩晕之锤","地狱之刃-眩晕","钢铁之躯","磁力场","磁力场-曲扭光环","磁力反击","沉默","清空目标子弹","减速","蝙蝠阵","巫尸之怒","巫尸之怒-吸血","毒爪","灵步","瞬秒1","缠绕","刚毅","践踏","铁拳","净化器","踩踏","无疆统治","无疆统治-吸附","无疆统治-闪电伤害"
         ,"群体狂暴","生命连结","反哺","反转术","七步毒","野性召唤","大地之怒","铁拳冲撞","灼热视线","狼图腾","疾风斩","遁地风暴","防弹外壳","防弹钢甲","防弹钢甲","绝命攻击","狂躁","喷射毒蛛","分身","狂暴","群体圣光","群体加速","瞬移","隐身","群体隐身","电离折射","爆石","闪电麻痹","定点轰炸","导弹召唤","万弹归宗","生命置换","沉默","全局溅射","嗜爪","毒爪","自燃","欺凌","馈赠","致残","减速","顽强","减速光环","致残光环","强击光环","腐蚀","荒芜光环","重生","复原","复原光环","反击","电离反转","瞬移","反馈--反弹被动技能","闪电麻痹-减速","生命置换-置换","技能复制-复制","全局溅射-链接","减速","只受到铁锹的攻击","只受到铁铲的攻击","只受圣诞礼炮攻击","不受任何元素伤害","血条等级显示倒计时","矿石产生物品","奉献","巨伤盾三","防御力提升20%","载具攻防","继续抵挡3次百分比伤害","减少无敌敌人伤害","靠近回血","主角抵挡3次百分比伤害","召唤毒蛛","反哺","反扑","电球爆发","永久隐身","酸雨","召唤毒蛛","增加血量","增加弹药","无敌药水","增加移动速度","召唤摩卡","无敌隐身-重生石","无敌隐身","月饼增加攻击力","汤圆增加攻击力","饺子无敌状态","双倍黑色武器掉落","双倍黑色装备掉落","双倍黑色武器掉落","双倍黑色装备掉落","神宠卡片","神车卡片","4倍载具伤害","磁力场","超级散射卡","骷髅卡","骷髅卡扣血","后来居上","夜空隐","狼震","金翼","龙翅","恶魔风脚","飞雷神","疾风斩","瞬秒2","小炎戒","英雄跳斩-降低目标攻击力","队长之魄","鼓舞士气","盛怒","无敌之怒","生化锁定","生化锁定X","动能推撞","超能推撞","挖掘者-动能推撞-推","技能衰减","竞技场-伤害加倍","击中减速","减速光环","主动加血","反弹伤害","怪物带着南关头","花火面具","沃龙面具","丛安面具","超级散射","无限馈赠","极限射速","战争狂人脱下头盔","丛林特种兵技能"
         ,"瞬秒3","攻击无视闪避","红外眼","无敌自爆僵尸攻击主角","狙击之王任务主角状态","狙击之王怪物攻击主角","狙击之王怪物受攻击","狙击之王怪物受攻击-技能","子弹碰撞","蝙蝠状态","步枪敏感","狙击敏感","散弹敏感","手枪敏感","火炮敏感","弩敏感","喷火器敏感","激光枪敏感","其他敏感","副手敏感","载具敏感","尸宠敏感","红武敏感","徒手敏感","巫尸之怒","猎击","酸性腐蚀","旋风刀","震地","狂刃追踪","远视","极速炮轰","补给头盔","弹性世界","全域圣光","聚能力量","飓风","巫尸之怒","牺牲","防御光环","上帝的护佑","无尽轰炸","聚能电流","退化光环","野性召唤","灼热射线","狼图腾","大地之怒","充能","辐射光球","静电过载","敏捷光环","致盲","减速","眩晕","灼热射线-附带技能","闪烁-目标点爆炸","自燃","狂暴","沉默","群体圣光","反击","电离折射","馈赠","先锋盾","电离反转","毒雾","定点轰炸","欺凌","闪烁","怒火","闪电麻痹","爆石","顽强","强击光环","复原光环","致残光环","减速光环","怪物猎人","精英克星","人类克星","恶魔猎手","恶魔屠刀","上帝之眼","上帝之杖","鬼步","上帝之眼-防御力降低","上帝之杖-防御力降低","闪避提升20%","防御力提升30%","主角攻击力提升10%","抵挡伤害","相同武器伤害叠加","攻击力提升30%","伤害光环150","雪藏","近战防御","千斤顶","千斤顶","白色血液效果","绿色血液效果","击中沉默","分身","风暴突袭","收到攻击打断风暴突袭","钩拉","钩雪莲华","钩拉-击中麻痹","钩雪莲华-击中","钩雪莲华-抛空中","腐蚀","飞雪连舞","雪引冲锋","爆怒一击","踢爆","召唤雪人","飞雪连舞","雪引冲锋-冲撞","踢爆-冲撞","飓风载体","诱击对玩家单位伤害降低","摩卡护体","敏捷馈赠","防御力提升","攻击力提升","真情之力","重生","回复","真情治愈","分子虹吸","分子虹吸-自身回血","荆棘外壳","血肉盛宴","雷霆斧击","野性冲锋","巨力滚"
         ,"上帝的护佑","免疫","磁力场","顽强光环","嗜爪之怒","瘴气","耐久光环","牺牲","钢背","负离子外壳","净化器","芒刺","荆棘外表","折射","召唤群狼","装甲压制","磁力场-曲扭光环","折射","嘲讽","遇强则刚","复仇","共鸣","暗夜信徒","恶爪","嘲讽敌人","反弹伤害","复仇伤害","击中减速","击毙补充弹药","击毙回复","剧毒","击毙溅射","振奋","击中溅射","致残","击中麻痹","致盲","击中回复","击中沉默","击中派生","瞬秒4","绝灭","快感","搏命","七步毒","爆石","引爆","刷新","击中闪电","击中眩晕","超级派生","超级麻痹","炎爆","冷凝","蚀骨","痛击","连弩","粘性","超级粘性","火焰始祖","贯穿波","无限火力","跳斩","上帝之杖","风雷","影灭","爆沙","爆胆","爆震","破甲","溃甲","飞镰","跟踪飞镰","战修罗","突袭","突袭","惩罚","子母弹","烟花","轨迹编辑","炎爆-爆","爆胆-无法叠加得buff","超级飞镰-破甲","轰天聚合","镇山聚合","霸空聚合","核弹头","核动力","冻血刀锋","守望之盾","合金外壳","遇强则强","冲击波","见面礼","降低攻击力","特效","特效","防御力提升","抵挡低于自身生命值的伤害。","抵挡4次百分比伤害","主角攻击力提升","攻击力提升40%","载具防御力","钢铁之躯","指定元素加成","掉率提升","狂战尸-震地减速","分身","狂刃爆发","召唤冥刃","分身","沉默","金钟雾","电离驱散","电离驱散-任何","加持","崩溃","凯撒特效附带","凯撒特效附带","石海buff","先锋盾","能量外壳","分身","副手防御","聚合防御","免疫","封锁","击落","受击坠落","统治圈","利刃盾","陨石雨","大地闪电","破宠","电离驱散","无敌驱散","超级散射","巨伤盾","尸毒","防空盾","激光盾","复仇之箭","复仇之魂","夺命箭","夺命魂","瞬秒所有除了载具","反击导弹","旋转电球","短命之仇","短命之仇-受到攻击","折寿","薄命","变尸"
         ,"死后杀死宿主","变蛛","竖盾","竖盾buff","强电","上帝之盾","锁血","瞬秒心零","监禁","禁无双","永久沉默","永久封锁","衰竭","群体圣光","狂暴","反击","派生导弹","吞噬","定点轰炸","远视","群体隐身","万弹归宗","金刚钻","毒雾","嗜爪","欺凌","魅惑","电离折射","馈赠","沉默","近视","先锋盾","全局溅射","群体自燃","精准","藐视","元素叠加","血盾","滑翔","翻滚","王者之翼","附身","全域光波","隐匿之雾","尖叫","反转术","全域圣光","妖魅","智慧怒火","毒雾-减血","吞噬-回血","反馈--反弹被动技能","自燃-链接","自燃-链接","翻滚-减速","沉默-使我方技能免疫3秒","击中眩晕","击中眩晕","清除燃气","燃气","原力俯冲","焦灼烈焰","巨石崩塌","硬甲","蛋护","海市蜃楼","饥荒","远古基因","解渴","巨焰围剿","月石","石化","矫捷姿态","召唤蝙蝠","龙甲","近战防御","石化眩晕","量子光束","末日轰炸","召唤生化罐","超导电幕","机械重组","超导电幕-第一阶段","超导电幕-第二阶段","超导电幕-怒气","超导电幕-防御","异角龙百分比伤害","召唤异角龙","死后清除所有召唤单位","电磁风暴","洲际导弹","洲际导弹-自爆","机械核心","钛钢甲","召唤机械核心","机械核心-死亡","机械核心-附着","机械核心-蓝闪电","机械核心-红闪电","机械核心-受伤","狼狈为奸","尸狼-死亡","无敌光环","磁力场","墙","强击光环","被击中麻痹","星际尘埃","星辰buff","解析射线","虚幻镜像","召唤随从","反哺","反物质","击中眩晕","击中死亡","恶魔火","自燃-链接","驾驭星火","击中麻痹","墟洞岩火","无名火","稀有元素","孪生之力","无法释放墟洞岩火buff","打断墟洞岩火","对玩家造成伤害","对分身造成伤害","使自身恐惧","使首领恐惧","水卷风","水汽溅腾","地精","融弹回血","反转气泡","眩晕","转变颜色","死亡","蓝","绿","弹跳","泰山压顶","蝎舞风暴"
         ,"战栗光","潮汐护甲","阳风增加攻击力","毒舌钩","炸裂者-舌头钩-击中","舌头钩-击中后移动则受到伤害","布雷风暴","地雷陷阱","暗影突袭","静电场","静电场-吸附","致命打击","超级弹幕","极速伤害","瘟疫","织网","织网-隐身","织网-回血","繁殖","复仇之刺","复仇之刺-击中被拖行","决斗者-特效预处理","旋风斩","旋风斩-减速","旋风波","决斗术","决斗术-击晕目标","分身术","召唤古惑"];
         var ArmsNow:* = null;