package UI.forging.equipRemake
{
   import UI.NormalUICtrl;
   import UI.UIOrder;
   import UI.bag.ItemsGrid;
   import UI.bag.ItemsGripBox;
   import UI.bag.ItemsGripTipCtrl;
   import UI.base.NormalUI;
   import UI.base.button.NormalBtn;
   import UI.base.event.ClickEvent;
   import UI.base.must.NormalMustBox;
   import UI.forging.equipUpgrade.EquipUpgradeBoard;
   import com.sounto.oldUtils.ComMethod;
   import dataAll.equip.EquipData;
   import dataAll.equip.EquipDataGroup;
   import dataAll.equip.creator.EquipRemakeCtrl;
   import dataAll.equip.creator.EquipSkillCreator;
   import dataAll.equip.define.EquipColor;
   import dataAll.equip.save.EquipSave;
   import dataAll.items.creator.OneProData;
   import dataAll.must.PlayerMustCtrl;
   import dataAll.must.define.MustDefine;
   import dataAll.skill.define.SkillDefine;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   
   public class EquipRemakeBoard extends NormalUI
   {
      
      private var copyData:EquipData;
      
      public var equipUpgradeBoard:EquipUpgradeBoard;
      
      private var proTag:Sprite;
      
      private var mustSp:Sprite;
      
      private var itemsGripSp:MovieClip;
      
      private var btnSp:MovieClip;
      
      private var gotoBackSp:MovieClip;
      
      private var proTxt:TextField;
      
      private var btn:NormalBtn = new NormalBtn();
      
      private var gotoBackBtn:NormalBtn = new NormalBtn();
      
      private var itemsGrip:ItemsGrid = new ItemsGrid();
      
      private var mustBox:NormalMustBox = new NormalMustBox();
      
      private var proBox:ItemsGripBox = new ItemsGripBox();
      
      private var _nowData:EquipData;
      
      private var nowProDataArr:Array = [];
      
      private var tempRemakeSave:EquipSave = null;
      
      private var beforeSave:EquipSave = null;
      
      public function EquipRemakeBoard()
      {
         super();
      }
      
      public function set nowData(da0:EquipData) : void
      {
         if(da0 != this._nowData || !da0)
         {
            this.nowProDataArr = [];
            this.beforeSave = null;
         }
         this.equipUpgradeBoard.nowData = da0;
         this._nowData = da0;
      }
      
      public function get nowData() : EquipData
      {
         return this.equipUpgradeBoard.nowData;
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["proTag","mustSp","btnSp","gotoBackSp","itemsGripSp","proTxt"];
         super.setImg(img0);
         addChild(this.btn);
         this.btn.setImg(this.btnSp);
         this.btn.setName("");
         addChild(this.gotoBackBtn);
         this.gotoBackBtn.setImg(this.gotoBackSp);
         this.gotoBackBtn.setName("编辑当前数据");
         addChild(this.itemsGrip);
         this.itemsGrip.setImg(this.itemsGripSp);
         ItemsGripTipCtrl.addEvent_byItemsGrip(this.itemsGrip);
         addChild(this.mustBox);
         this.mustBox.setImg(this.mustSp);
         addChild(this.proBox);
         NormalUICtrl.setTag(this.proBox,this.proTag);
         this.proBox.arg.init(1,8,0,4);
         this.proBox.evt.setWantEvent(true,false,false,true,true);
         this.proBox.setIconPro("ForgingUI/armsProBar",50,50);
         this.proBox.addEventListener(ClickEvent.ON_CLICK,this.proBarClick);
         this.proBox.addEventListener(ClickEvent.ON_OVER,this.proBarOver);
         this.proBox.addEventListener(ClickEvent.ON_OUT,this.proBarOut);
         this.btn.addEventListener(MouseEvent.CLICK,this.btnClick);
         this.btn.addEventListener(MouseEvent.MOUSE_OVER,this.btnOver);
         this.btn.addEventListener(MouseEvent.MOUSE_OUT,this.proBarOut);
         this.gotoBackBtn.addEventListener(MouseEvent.CLICK,this.gotoBackBtnClick);
         this.gotoBackBtn.addEventListener(MouseEvent.MOUSE_OVER,this.btnOver);
         this.gotoBackBtn.addEventListener(MouseEvent.MOUSE_OUT,this.proBarOut);
         this.showNone();
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override public function show() : void
      {
         super.show();
         Gaming.uiGroup.allBagUI.chooseItemsData_out(this.nowData,"equip");
         this.showOneEquipDataAndPan(this.nowData);
      }
      
      public function outLoginEvent() : void
      {
         this.showNone();
      }
      
      public function equipGripClick(da0:EquipData) : void
      {
         if(visible)
         {
            this.showOneEquipDataAndPan(da0);
         }
      }
      
      private function proBarClick(e:ClickEvent) : void
      {
         if(this.proBox.gripArr.length == 1)
         {
            return;
         }
         var grip0:ItemsGrid = e.child as ItemsGrid;
         var da0:OneProData = e.childData as OneProData;
         if(!da0.noChangeLockB)
         {
            da0.lockB = !da0.lockB;
         }
         grip0.inData_proData(da0);
         this.fleshMust();
      }
      
      private function proBarOver(e:ClickEvent) : void
      {
         var d0:SkillDefine = null;
         var str0:String = null;
         this.proBarOut(e);
         var da0:OneProData = e.childData as OneProData;
         if(da0.type == "skill")
         {
            d0 = Gaming.defineGroup.skill.getDefine(da0.name);
            str0 = ComMethod.color("<b>" + d0.cnName + "</b>","#FFFF00");
            Gaming.uiGroup.tipBox.textTip.showFollowText(str0 + "\n" + d0.getDescription());
         }
      }
      
      private function proBarOut(e:Event) : void
      {
         Gaming.uiGroup.tipBox.hide();
      }
      
      private function btnOver(e:Event) : void
      {
         var str0:String = "";
         if(e.target != this.btn)
         {
            if(e.target == this.gotoBackBtn)
            {
               str0 = "除了装备时装外其他equip不得编辑！";
            }
         }
         if(str0 == "")
         {
            this.proBarOut(e);
         }
         else
         {
            Gaming.uiGroup.tipBox.textTip.showFollowText(str0);
         }
      }
      
      private function showOneEquipDataAndPan(da0:EquipData, fleshNowProDataArrB0:Boolean = false) : void
      {
         var dg0:EquipDataGroup = null;
         this.btn.setName("复制装备");
         Gaming.uiGroup.forgingUI.setCoverText("请在右边选择要复制的装备。");
         if(this.copyData != null)
         {
            this.btn.actived = true;
            this.btn.setName("添加装备");
            Gaming.uiGroup.forgingUI.setCoverText("");
         }
         if(da0)
         {
            dg0 = Gaming.PG.da.findEquipData(da0,false);
            if(dg0 is EquipDataGroup)
            {
               this.showOneEquipData(da0,fleshNowProDataArrB0);
            }
            Gaming.uiGroup.forgingUI.setCoverText("");
         }
      }
      
      private function showOneEquipData(da0:EquipData, fleshNowProDataArrB0:Boolean = false) : void
      {
         this.nowData = da0;
         this.itemsGrip.inData_equip(da0);
         if(fleshNowProDataArrB0 || this.nowProDataArr.length == 0)
         {
            this.nowProDataArr = EquipSkillCreator.getOneProDataArr(da0.save);
         }
         this.proBox.inData_byArr(this.nowProDataArr,"inData_proData");
         if(this.nowProDataArr.length == 1)
         {
            this.proBox.setAllFun("setShopBtnBackMc","");
         }
         this.fleshMust();
      }
      
      private function fleshMust() : void
      {
         var s0:EquipSave = this.nowData.save;
         var must_d0:MustDefine = EquipRemakeCtrl.getMust(this.nowData.save,this.nowProDataArr);
         var mustB0:Boolean = this.mustBox.inData(must_d0);
         var lockAllB0:Boolean = this.proIsLockAllB();
         var colorB0:Boolean = EquipColor.firstMax(this.nowData.getColor(),"orange",true);
         this.btn.actived = true;
         this.gotoBackBtn.visible = true;
         this.proTxt.text = "";
         if(!colorB0)
         {
            this.proTxt.htmlText = ComMethod.color("点击复制装备即可复制当前装备","#FF0000");
         }
         else if(this.nowProDataArr.length == 0)
         {
            this.proTxt.text = "";
         }
      }
      
      private function proIsLockAllB() : Boolean
      {
         var da0:OneProData = null;
         if(this.nowProDataArr.length == 0)
         {
            return false;
         }
         for each(da0 in this.nowProDataArr)
         {
            if(!da0.lockB)
            {
               return false;
            }
         }
         return true;
      }
      
      private function showNone() : void
      {
         this.nowData = null;
         this.beforeSave = null;
         this.nowProDataArr = [];
         this.tempRemakeSave = null;
         this.itemsGrip.clearData();
         this.proBox.inData_byArr([],"inData_proData");
         this.mustBox.setShowState(false);
         this.btn.actived = true;
         this.gotoBackBtn.visible = true;
      }
      
      private function btnClick(e:MouseEvent) : void
      {
         var s0:EquipSave = new EquipSave();
         if(this.copyData == null)
         {
            this.copyData = this.nowData;
            Gaming.uiGroup.alertBox.showSuccess("复制当前装备数据成功！");
            this.showOneEquipDataAndPan(this.nowData);
         }
         else
         {
            s0.inData_byObj(this.copyData.save);
            Gaming.PG.da.equipBag.addSave(s0);
            Gaming.uiGroup.alertBox.showSuccess("添加装备成功！");
            this.copyData = null;
            this.showOneEquipDataAndPan(this.nowData);
         }
      }
      
      private function gotoBackBtnClick(e:MouseEvent) : void
      {
         var s0:EquipSave = new EquipSave();
         s0.inData_byObj(this.nowData.save);
         if(this.nowData)
         {
            if(s0.partType == "fashion" || s0.partType == "coat" || s0.partType == "pants" || s0.partType == "head" || s0.partType == "belt")
            {
               Gaming.uiGroup.alertBox.textInput.showTextInput("🔧 装备编辑菜单 🔧\n\n📋 基础属性编辑:\n等级[00*数值] 颜色[01*颜色] 强化[02*数值]\n进化[03*数值] 类型[04*类型] 基础等级[05*数值]\n获取时间[06*日期] 到期时间[07*日期] 永不过期[08]\n\n⚔️ 技能编辑:\n技能添加[09*代码] 技能删除[10*代码]\n平衡技能组合[15] 神级技能组合[16]\n实用技能组合[17] 不死装技能组合[18]\n清空所有技能[13] ⚠️全部技能[12]\n\n🎁 属性编辑菜单:\n掉落属性页[20] 战斗属性页[21] 特殊属性页[22]\n超级属性包[23] 清空所有属性[24]\n\n🔧 调试功能:\n测试属性显示[99]\n\n🔄 其他:\ncopyData清空[11]\n\n输入示例: 02*27&01*红&20","",this.EquipEdit_Equip);
            }
         }
         else
         {
            Gaming.uiGroup.alertBox.showError("装备数据不存在！");
         }
      }
      
      // 获取所有正面技能（已移除负面技能）
      private function getAllSkills() : Array
      {
         return [
            // 武器增益技能
            "Kill_AddCharger_ArmsSkill","Kill_AddLifeMul_ArmsSkill","Hit_AddLifeMul_ArmsSkill","Hit_hitMissile_godArmsSkill","Hit_seckill_godArmsSkill","Hit_finalkill_godArmsSkill","Hit_crazy_godArmsSkill","Hit_atry_godArmsSkill","Hit_imploding_godArmsSkill","Hit_pointBoom_godArmsSkill","Hit_fleshSkill_godArmsSkill","Hit_Lightning_godArmsSkill","Hit_Hammer_godArmsSkill","Hit_SuperMissile_godArmsSkill","combo_ArmsSkill","viscous_ArmsSkill","viscousSuper_ArmsSkill","flyDragonHead","bangerGunSkill","infiniteFire","shotgunBlade_ArmsSkill","godMace_ArmsSkill","windThunder_ArmsSkill","laserKill_godArmsSkill","noFoggyDef","immuneNemesis","lash_ArmsSkill","beadCrossbow_ArmsSkill","editBulletPath","Hit_burn_ArmsSkill_link",
            // 联盟增益技能
            "watchmanHurtNormal","watchmanHurtSuper","watchmanHurtHuman","watchmanHurtBoss","superWatchmanHurtBoss","godEyes","godMace","heroSprint","godEyesDefence","godMaceDefence","dodgeProZang","defenceZang","hurtStrikerZang","noHurtZang","sameArmsHurtAddZang","hurtZang","hurtHoleZang",
            // 90级增益技能
            "SnowThinRuin","fightReduct2","SnowFattySprint","SnowFattySprintHit","hitBloodWhite","hitBloodGreen","clonedSnowSoldiers","teleport_SnowSoldiers","underCrossbow_SnowSoldiers","SnowGirlHook","SnowGirlPull","SnowGirlHookHit","SnowGirlPullHit","SnowGirlAfterPullHit","IceManRotate","IceManStrike","IceManShake","IceManKick","IceManSnowman","IceManRotateLink","IceManStrikeHit","IceManKickHit","snowWind","RifleHornetShooterHurt","escapeInvincible","dodge_loveSkill","girlDefence_loveSkill","girlHurt_loveSkill","lovePower_loveSkill","rebirth_loveSkill","addLifeMul_loveSkill","lifeBottle_loveSkill","element_loveSkill","elementLifeBack_loveSkill",
            // 装备正面技能（移除了backWeak_equip和sacrifice_equip等负面技能）
            "godHand_equip","immune_equip","magneticField_equip","strongHalo_equip","murderous_equip","poisonRange_equip","attackSpeedHalo_equip","backStrong_equip","anionSkin_equip","treater_equip","thornSkin_equip","refraction_equip","summonWolf_bigBoss","zoomOut","magneticField_enemy_link","refraction_equip_link",
            // 宠物增益技能
            "flash_pet_link","groupLight_pet","tenacious_pet","feedback_pet","skillGift_pet","pioneer_pet","groupReverseHurt_pet","pointBoom_pet","bullying_pet","flash_pet","strong_pet","trueshot_pet","recoveryHalo_pet",
            // 载具增益技能
            "vehicleFit_Gaia","vehicleFit_Civilian","vehicleFit_fly","crazy_vehicle","murderous_vehicle","blade_blueMoto","WatchEagleAirDefence","alloyShell","strongStrong","shockWave","meetingGift","blade_blueMoto_link","blueMoto_state","redMoto_state",
            // 兵器增益技能
            "weaponDefence","fitVehicleDefence","immune","toLand","underToLand","ruleRange","bladeShield","meteoriteRain","lightningFloor","invincibleEmp","moreBullet","resistMulHurt","noUnderFlyHit","noUnderLaser","revengeArrow","revengeGhost","deadlyArrow","deadlyGhost","killAllExcludeVehicle","fightBackBullet","screwBall","summonShortLife","summonShortLifeMax","verShield","verShieldBuff","midLightning","godShield","lockLife",
            // 时装增益技能
            "outfit_follow","outfit_blood","outfit_shootMissile","outfit_jump","outfit_crit","outfit_boom","outfit_wolong","outfit_wolong_hurt","outfit_eagle","outfit_elephant",
            // 零件增益技能
            "huntParts","acidicParts",
            // 通用增益技能
            "DropEffect_AddLifeMul","DropEffect_AddChargerMul","invincibleDrugDrop","DropEffect_AddMoveSpeedMul","addMocha","godHiding_things","godHiding_Pet","moonCake","tangyuan","jiaozi","armsDropDouble","equipDropDouble","armsDropDoubleAndGem","equipDropDoubleAndEquipGem","highPetCard","highVehicleCard","highCardState","electromagnet","superSpreadCard","skeletonCard","skeletonCard_link","pumpkinHead","wolfFashionSkill","goldFalcon","dragonHeadSkill","crazy_sanji","xiaoBoShoot","xiaoMingShoot","seckillNormalEnemy","xiaoAiShoot","shotgunBladeHero","chinaCaptainSkill","armyCommanderSkill","bladeSkill","cyanArmySkill","bioShockSkill","hurtBossAdd","highDrill","superHighDrill","highDrillHit","extendCd","arenaHurtAdd","Manual_AddLife","BackHurt","pumpkinDropEffect","gmMask1","gmMask2","gmMask3","superMoreBullet","superSkillGift","maxSpeedTask","madmanHead","betHit","killAll","attackNoDodge","findHide","invincibleZombieEnemyHit","sniperKingBuff","sniperKingEnemyHit","sniperKingEnemyUnder","sniperKingEnemyUnder2","bulletRainBallHit","flySkyBatBuff","rifleSensitive","sniperSensitive","shotgunSensitive","pistolSensitive","rocketSensitive","crossbowSensitive","flamerSensitive","laserSensitive","otherSensitive","weaponSensitive","vehicleSensitive","petSensitive","redArmsSensitive","handSensitive","wizardAngerEdit"
         ];
      }

      // 获取平衡的装备技能组合 (88个技能)
      private function getBalancedEquipSkills() : Array
      {
         return [
            // 装备基础技能 (18个)
            "godHand_equip","immune_equip","magneticField_equip","strongHalo_equip","murderous_equip",
            "poisonRange_equip","attackSpeedHalo_equip","sacrifice_equip","backStrong_equip","anionSkin_equip",
            "treater_equip","backWeak_equip","thornSkin_equip","refraction_equip","summonWolf_bigBoss",
            "zoomOut","magneticField_enemy_link","refraction_equip_link",
            // 武器技能 (30个)
            "Hit_SlowMove_ArmsSkill","Kill_AddCharger_ArmsSkill","Hit_Poison_ArmsSkill","Hit_disabled_ArmsSkill",
            "Hit_Paralysis_ArmsSkill","Hit_silence_ArmsSkill","Hit_blindness_ArmsSkill","Hit_Spurting_ArmsSkill",
            "Hit_burn_ArmsSkill","cold_ArmsSkill","erosion_ArmsSkill","beatBack_ArmsSkill",
            "combo_ArmsSkill","viscous_ArmsSkill","viscousSuper_ArmsSkill","infiniteFire","godMace_ArmsSkill",
            "windThunder_ArmsSkill","shotgunBlade_ArmsSkill","bangerGunSkill","flyDragonHead",
            "Hit_AddLifeMul_ArmsSkill","Kill_AddLifeMul_ArmsSkill","Kill_Spurting_ArmsSkill","Kill_Crazy_ArmsSkill",
            "Hit_Paralysis_ArmsSkill2","lash_ArmsSkill","beadCrossbow_ArmsSkill","editBulletPath","Hit_burn_ArmsSkill_link",
            // 联盟技能 (17个)
            "watchmanHurtNormal","watchmanHurtSuper","watchmanHurtHuman","watchmanHurtBoss","superWatchmanHurtBoss",
            "godEyes","godMace","heroSprint","godEyesDefence","godMaceDefence","dodgeProZang","defenceZang",
            "hurtStrikerZang","noHurtZang","sameArmsHurtAddZang","hurtZang","hurtHoleZang",
            // 宠物技能 (13个)
            "flash_pet_link","selfBurn_pet","crazy_pet","silence_pet","groupLight_pet","tenacious_pet",
            "feedback_pet","skillGift_pet","pioneer_pet","groupReverseHurt_pet","poisonousFog_pet","pointBoom_pet","bullying_pet",
            // 通用技能 (10个)
            "DropEffect_AddLifeMul","DropEffect_AddChargerMul","invincibleDrugDrop","DropEffect_AddMoveSpeedMul",
            "addMocha","godHiding_things","godHiding_Pet","moonCake","tangyuan","jiaozi"
         ];
      }

      // 获取神级技能组合 (130个技能)
      private function getGodSkills() : Array
      {
         return [
            // 神级武器技能 (25个)
            "Hit_seckill_godArmsSkill","Hit_finalkill_godArmsSkill","Hit_crazy_godArmsSkill","Hit_atry_godArmsSkill",
            "Hit_posion7_godArmsSkill","Hit_imploding_godArmsSkill","Hit_pointBoom_godArmsSkill","Hit_fleshSkill_godArmsSkill",
            "Hit_Lightning_godArmsSkill","Hit_Hammer_godArmsSkill","Hit_SuperMissile_godArmsSkill","Hit_hitMissile_godArmsSkill",
            "laserKill_godArmsSkill","imploding_blackArmsSkill","fear_godArmsSkill","fear_godArmsSkill2",
            "sickle_godArmsSkill","sickle_godArmsSkill2","demonAddHurt","immuneNemesis","noFoggyDef",
            "lash_ArmsSkill","beadCrossbow_ArmsSkill","editBulletPath","Hit_burn_ArmsSkill_link",
            // 联盟神级技能 (17个)
            "superWatchmanHurtBoss","godEyes","godMace","heroSprint","godEyesDefence","godMaceDefence",
            "watchmanHurtNormal","watchmanHurtSuper","watchmanHurtHuman","watchmanHurtBoss",
            "dodgeProZang","defenceZang","hurtStrikerZang","noHurtZang","sameArmsHurtAddZang","hurtZang","hurtHoleZang",
            // 90级技能 (36个)
            "SnowThinRuin","fightReduct2","SnowFattySprint","SnowFattySprintHit","hitBloodWhite","hitBloodGreen",
            "silence_SnowSoldiers","clonedSnowSoldiers","teleport_SnowSoldiers","underCrossbow_SnowSoldiers",
            "SnowGirlHook","SnowGirlPull","SnowGirlHookHit","SnowGirlPullHit","SnowGirlAfterPullHit",
            "IceManCorrosion","IceManRotate","IceManStrike","IceManShake","IceManKick","IceManSnowman",
            "IceManRotateLink","IceManStrikeHit","IceManKickHit","snowWind","RifleHornetShooterHurt",
            "escapeInvincible","dodge_loveSkill","girlDefence_loveSkill","girlHurt_loveSkill",
            "lovePower_loveSkill","rebirth_loveSkill","addLifeMul_loveSkill","lifeBottle_loveSkill",
            "element_loveSkill","elementLifeBack_loveSkill",
            // 装备神级技能 (18个)
            "godHand_equip","immune_equip","magneticField_equip","strongHalo_equip","murderous_equip",
            "poisonRange_equip","attackSpeedHalo_equip","sacrifice_equip","backStrong_equip","anionSkin_equip",
            "treater_equip","backWeak_equip","thornSkin_equip","refraction_equip","summonWolf_bigBoss",
            "zoomOut","magneticField_enemy_link","refraction_equip_link",
            // 兵器技能 (20个)
            "weaponDefence","fitVehicleDefence","immune","offAllSkill","toLand","underToLand","ruleRange",
            "bladeShield","meteoriteRain","lightningFloor","killPet","enemyEmp","invincibleEmp",
            "moreBullet","resistMulHurt","godShield","lockLife","killXinLing","cantMove","noPurgoldArms",
            // 载具技能 (14个)
            "vehicleFit_Gaia","vehicleFit_Civilian","vehicleFit_fly","crazy_vehicle","murderous_vehicle",
            "blade_blueMoto","WatchEagleAirDefence","alloyShell","strongStrong","shockWave",
            "meetingGift","blade_blueMoto_link","blueMoto_state","redMoto_state"
         ];
      }

      // 获取实用技能组合 (100个技能)
      private function getUtilitySkills() : Array
      {
         return [
            // 增益技能 (25个)
            "DropEffect_AddLifeMul","DropEffect_AddChargerMul","invincibleDrugDrop","DropEffect_AddMoveSpeedMul",
            "addMocha","godHiding_things","godHiding_Pet","moonCake","tangyuan","jiaozi",
            "Manual_AddLife","BackHurt","addLifeMul_loveSkill","lifeBottle_loveSkill","element_loveSkill",
            "elementLifeBack_loveSkill","nearAddLifeLove","mainResistPerLove","resistPerLove2","defenceAddLove",
            "vehicleAddLove30","resistPerLoveAdd2","underInvincibleHurtLove","huntParts","acidicParts",
            // 掉落技能 (20个)
            "armsDropDouble","equipDropDouble","armsDropDoubleAndGem","equipDropDoubleAndEquipGem",
            "highPetCard","highVehicleCard","highCardState","electromagnet","superSpreadCard",
            "skeletonCard","skeletonCard_link","pumpkinHead","wolfFashionSkill","goldFalcon",
            "pumpkinDropEffect","gmMask1","gmMask2","gmMask3","orcCreateThings","dedicationLove",
            // 战斗增强 (25个)
            "superMoreBullet","superSkillGift","killAll","attackNoDodge","findHide",
            "maxSpeedTask","madmanHead","betHit","invincibleZombieEnemyHit","sniperKingBuff",
            "bulletRainBallHit","flySkyBatBuff","dragonHeadSkill","crazy_sanji","xiaoBoShoot",
            "xiaoMingShoot","seckillNormalEnemy","xiaoAiShoot","shotgunBladeHero","chinaCaptainSkill",
            "armyCommanderSkill","bladeSkill","cyanArmySkill","bioShockSkill","hurtBossAdd",
            // 装备专用技能 (15个)
            "godHand_equip","immune_equip","magneticField_equip","strongHalo_equip","attackSpeedHalo_equip",
            "sacrifice_equip","anionSkin_equip","treater_equip","thornSkin_equip","refraction_equip",
            "murderous_equip","poisonRange_equip","backStrong_equip","summonWolf_bigBoss","zoomOut",
            // 移动技能 (15个)
            "State_AddMove","State_AddMove50","jumpNumAdd1","UnderRos_AddMove_Battle","addMove_Crawler",
            "groupSpeedUp_enemy","teleport_enemy","invisibility_enemy","hyperopia_incapable","through_enemy",
            "noBulletReduct","throughClose","noSpeedReduce","agile_PetLake","heroSprint"
         ];
      }

      // 获取不死装技能组合 (专门的生存技能)
      private function getImmortalSkills() : Array
      {
         return [
            // 无敌类技能
            "State_Invincible","State_InvincibleThrough","invincibleDrugDrop","escapeInvincible",
            "invincibleEmp","godHiding_things","godHiding_Pet","invincibleZombieEnemyHit",
            // 重生复活类技能
            "rebirth_loveSkill","rebirth_enemy","addLifeMul_loveSkill","lifeBottle_loveSkill",
            // 生命回复类技能
            "DropEffect_AddLifeMul","Manual_AddLife","nearAddLifeLove","elementLifeBack_loveSkill",
            "treater_equip","treater_knights","treater_FightWolf","recoveryHalo_pet","recovery_enemy",
            "recoveryHalo_enemy","treater_PetFightWolf","laser_PetFightWolf","treater_pet",
            // 伤害减免类技能
            "resistPerLove2","resistPerLoveAdd2","mainResistPerLove","resistMulHurt","noHurtZang",
            "defenceAddLove","defenceZang","godEyesDefence","godMaceDefence","girlDefence_loveSkill",
            // 特殊保护类技能
            "lockLife","godShield","immune","immune_equip","State_SpellImmunity","noSkillHurt",
            "noBulletHurt","onlyWeaponHurt","onlyWeaponHurtBattle","onlyWeaponHurtSet","noEleUnder",
            // 防御增强类技能
            "strongHalo_equip","backStrong_equip","anionSkin_equip","thornSkin_equip","refraction_equip",
            "magneticField_equip","godHand_equip","defenceAuras_PetIronChief","godHand_PetIronChief"
         ];
      }

      private function EquipEdit_Equip(str0:String) : void
      {
         Gaming.uiGroup.alertBox.showSuccess("收到输入: " + str0);
         var i:int = 0;
         var j:int = 0;
         var k:int = 0;
         var allSkills:Array = this.getAllSkills();
         var ArrColor0:Array = ["white","green","blue","purple","orange","red","black","darkgold","purgold","yagold"];
         var ArrColor1:Array = ["白","绿","蓝","紫","橙","红","黑","暗金","紫金","氩星"];
         var Arr_type0:Array = ["coat","pants","head","belt","fashion"];
         var Arr_type1:Array = ["衣服","裤子","头盔","腰带","时装"];
         var s0:EquipSave = new EquipSave();
         var ArrNum:Array = new Array();
         var ArrNow:Array = new Array();
         var EquipNow:String = null;
         ArrNum = str0.split("&",str0.length);
         s0.inData_byObj(this.nowData.save);
         for each(EquipNow in ArrNum)
         {
            ArrNow = EquipNow.split("*",EquipNow.length);
            if(ArrNow[0] == "00")
            {
               s0.addLevel = Number(ArrNow[1]);
            }
            else if(ArrNow[0] == "01")
            {
               i = 0;
               while(i < ArrColor0.length)
               {
                  if(ArrNow[1] == ArrColor1[i])
                  {
                     s0.color = ArrColor0[i];
                  }
                  i++;
               }
            }
            else if(ArrNow[0] == "02")
            {
               s0.strengthenLv = Number(ArrNow[1]);
            }
            else if(ArrNow[0] == "03")
            {
               s0.evoLv = Number(ArrNow[1]);
            }
            else if(ArrNow[0] == "04")
            {
               j = 0;
               while(j < Arr_type0.length)
               {
                  if(ArrNow[1] == Arr_type1[j])
                  {
                     s0.partType = Arr_type0[j];
                  }
                  j++;
               }
            }
            else if(ArrNow[0] == "05")
            {
               s0.itemsLevel = Number(ArrNow[1]);
            }
            else if(ArrNow[0] == "06")
            {
               s0.getTime = ArrNow[1];
            }
            else if(ArrNow[0] == "07")
            {
               s0.severTime = ArrNow[1];
            }
            else if(ArrNow[0] == "08")
            {
               s0.severTime = "9999-9-9 12:00:00";
            }
            else if(ArrNow[0] == "09")
            {
               s0.skillArr.push(ArrNow[1]);
            }
            else if(ArrNow[0] == "10")
            {
               s0.skillArr.splice(s0.skillArr.indexOf(ArrNow[1]),1);
            }
            else if(ArrNow[0] == "11")
            {
               this.copyData = null;
               Gaming.uiGroup.alertBox.showSuccess("copyData = null");
            }
            else if(ArrNow[0] == "15")
            {
               // 平衡技能组合
               var balancedSkills:Array = this.getBalancedEquipSkills();
               s0.skillArr = [];
               for(k = 0; k < balancedSkills.length; k++)
               {
                  s0.skillArr.push(balancedSkills[k]);
               }
               Gaming.uiGroup.alertBox.showSuccess("已添加平衡技能组合(" + balancedSkills.length + "个技能)！");
            }
            else if(ArrNow[0] == "16")
            {
               // 神级技能组合
               var godSkills:Array = this.getGodSkills();
               s0.skillArr = [];
               for(k = 0; k < godSkills.length; k++)
               {
                  s0.skillArr.push(godSkills[k]);
               }
               Gaming.uiGroup.alertBox.showSuccess("已添加神级技能组合(" + godSkills.length + "个技能)！");
            }
            else if(ArrNow[0] == "17")
            {
               // 实用技能组合
               var utilitySkills:Array = this.getUtilitySkills();
               s0.skillArr = [];
               for(k = 0; k < utilitySkills.length; k++)
               {
                  s0.skillArr.push(utilitySkills[k]);
               }
               Gaming.uiGroup.alertBox.showSuccess("已添加实用技能组合(" + utilitySkills.length + "个技能)！");
            }
            else if(ArrNow[0] == "18")
            {
               // 不死装技能组合
               var immortalSkills:Array = this.getImmortalSkills();
               s0.skillArr = [];
               for(k = 0; k < immortalSkills.length; k++)
               {
                  s0.skillArr.push(immortalSkills[k]);
               }
               Gaming.uiGroup.alertBox.showSuccess("已添加不死装技能组合(" + immortalSkills.length + "个技能)！");
            }
            else if(ArrNow[0] == "13")
            {
               // 清空所有技能
               s0.skillArr = [];
               Gaming.uiGroup.alertBox.showSuccess("已清空所有技能！");
            }
            else if(ArrNow[0] == "12" || ArrNow[0] == "12*")
            {
               // ⚠️慎用⚠️全部技能
               Gaming.uiGroup.alertBox.showWarning("警告：添加全部" + allSkills.length + "个技能可能导致游戏卡顿或崩溃！建议使用技能组合。");
               s0.skillArr = [];
               for(k = 0; k < allSkills.length; k++)
               {
                  s0.skillArr.push(allSkills[k]);
               }
               Gaming.uiGroup.alertBox.showSuccess("已添加全部" + allSkills.length + "个技能！(请谨慎使用)");
            }
            else if(ArrNow[0] == "20")
            {
               // 掉落属性页
               this.showDropPropertiesMenu();
               return; // 不继续处理，等待用户选择
            }
            else if(ArrNow[0] == "21")
            {
               // 战斗属性页
               this.showCombatPropertiesMenu();
               return; // 不继续处理，等待用户选择
            }
            else if(ArrNow[0] == "22")
            {
               // 特殊属性页
               this.showSpecialPropertiesMenu();
               return; // 不继续处理，等待用户选择
            }
            else if(ArrNow[0] == "23")
            {
               // 超级属性包
               this.addSuperPropertiesPackage(s0);
            }
            else if(ArrNow[0] == "24")
            {
               // 清空所有属性
               this.clearAllProperties(s0);
            }
            else if(ArrNow[0] == "99")
            {
               // 测试属性显示
               this.testPropertyDisplay(s0);
            }
         }
         this.nowData.save = s0;
         this.showOneEquipDataAndPan(this.nowData);

         // 强制刷新装备属性合并
         if(this.nowData.placeType == "wear")
         {
            this.nowData.normalPlayerData.fleshAllByEquip();
            Gaming.uiGroup.alertBox.showSuccess("装备属性编辑成功！已刷新穿戴装备属性！");
         }
         else
         {
            Gaming.uiGroup.alertBox.showSuccess("装备属性编辑成功！请重新穿戴装备以生效！");
         }
      }
      
      private function affterGotoBack() : void
      {
         this.remakeBySave(this.beforeSave);
         this.beforeSave = null;
         Gaming.soundGroup.playSound("uiSound","getItems");
      }
      
      private function affterGetStoreState(v0:int) : void
      {
         var must_d0:MustDefine = null;
         if(v0 == 1 || v0 == -2)
         {
            Gaming.uiGroup.connectUI.hide();
            must_d0 = EquipRemakeCtrl.getMust(this.nowData.save,this.nowProDataArr);
            PlayerMustCtrl.deductMust(must_d0,this.afterRemake);
         }
      }
      
      private function remakeBySave(s0:EquipSave) : void
      {
         EquipRemakeCtrl.setAllByOther(this.nowData.save,s0);
         if(this.nowData.placeType == "wear")
         {
            this.nowData.normalPlayerData.fleshAllByEquip();
         }
         var nowProDataArr2:Array = EquipSkillCreator.getOneProDataArr(this.nowData.save);
         OneProData.setLockByOther(nowProDataArr2,this.nowProDataArr);
         this.nowProDataArr = nowProDataArr2;
         this.showOneEquipDataAndPan(this.nowData,false);
         Gaming.uiGroup.allBagUI.fleshAllBox();
      }
      
      private function afterRemake() : void
      {
         this.beforeSave = new EquipSave();
         this.beforeSave.inData_byObj(this.nowData.save);
         this.remakeBySave(this.tempRemakeSave);
         ++Gaming.PG.save.headCount.equipRemakeNum;
         UIOrder.save(true,false,false,null,null,false,true);
      }

      // ==================== 属性编辑功能 ====================

      // 显示掉落属性菜单
      private function showDropPropertiesMenu() : void
      {
         var menuText:String = "🎁 掉落属性编辑菜单 🎁\n\n";
         menuText += "💎 特殊掉落 (输入格式: 属性代码*数值):\n";
         menuText += "无双水晶[30*999] 万能球[31*999] 战神之心[32*999]\n";
         menuText += "优胜券[33*999] 载具碎片[34*999]\n\n";
         menuText += "🧪 材料掉落:\n";
         menuText += "生命催化剂[35*999] 神能石[36*999] 转化石[37*999]\n";
         menuText += "化石[38*999] 血手[39*999] 装置[40*999]\n\n";
         menuText += "⚔️ 装备掉落:\n";
         menuText += "武器碎片[41*999] 装备碎片[42*999] 稀有装备[43*999]\n";
         menuText += "特殊零件[44*999] 宝石[45*999] 基因体[46*999]\n\n";
         menuText += "💰 经验金币:\n";
         menuText += "经验获取[47*999] VIP经验[48*999] 银币获取[49*999]\n";
         menuText += "幸运值[50*999]\n\n";
         menuText += "输入示例: 30*999&31*999&50*999";

         Gaming.uiGroup.alertBox.textInput.showTextInput(menuText, "", this.handleDropProperties);
      }

      // 显示战斗属性菜单
      private function showCombatPropertiesMenu() : void
      {
         var menuText:String = "⚔️ 战斗属性编辑菜单 ⚔️\n\n";
         menuText += "💥 伤害属性 (输入格式: 属性代码*数值):\n";
         menuText += "基础伤害[60*99999] 伤害倍率[61*99] 全伤害[62*15]\n";
         menuText += "暴击概率[63*50] 攻击间隔[64*0.1]\n\n";
         menuText += "❤️ 生存属性:\n";
         menuText += "生命值[65*99999] 生命倍率[66*99] 生命恢复[67*999]\n";
         menuText += "头部防护[68*99999] 闪避[69*50]\n\n";
         menuText += "🛡️ 防御属性:\n";
         menuText += "格斗减伤[70*65] 防弹值[71*39] 技能减伤[72*90]\n\n";
         menuText += "🔫 武器属性:\n";
         menuText += "弹夹容量[73*999] 充能速度[74*999] 换弹速度[75*0.01]\n\n";
         menuText += "🏃 移动属性:\n";
         menuText += "移动速度[76*99] 跳跃次数[77*10]\n\n";
         menuText += "输入示例: 60*99999&65*99999&71*39";

         Gaming.uiGroup.alertBox.textInput.showTextInput(menuText, "", this.handleCombatProperties);
      }

      // 显示特殊属性菜单
      private function showSpecialPropertiesMenu() : void
      {
         var menuText:String = "✨ 特殊属性编辑菜单 ✨\n\n";
         menuText += "💕 好感度属性 (输入格式: 属性代码*数值):\n";
         menuText += "赠礼好感度[80*999] 每日好感度[81*999]\n\n";
         menuText += "🎯 功能属性:\n";
         menuText += "每日扫荡次数[82*999]\n\n";
         menuText += "🚗 载具属性:\n";
         menuText += "载具防御[83*99] 载具伤害[84*99]\n\n";
         menuText += "👥 队友属性:\n";
         menuText += "队友伤害[85*99] 队友生命[86*99]\n\n";
         menuText += "🎮 特殊效果:\n";
         menuText += "伤害倍增器[87*99] 冷却缩减[88*99]\n\n";
         menuText += "输入示例: 80*999&81*999&82*999";

         Gaming.uiGroup.alertBox.textInput.showTextInput(menuText, "", this.handleSpecialProperties);
      }

      // 添加超级属性包
      private function addSuperPropertiesPackage(s0:EquipSave) : void
      {
         // 获取当前obj的副本
         var currentObj:Object = s0.obj;
         if(!currentObj) currentObj = {};

         // 添加你要求的所有属性
         currentObj["demStroneDropNum"] = 999;        // 无双水晶掉落 +999个
         currentObj["demBallDropNum"] = 999;          // 万能球掉落 +999个
         currentObj["madheartDropNum"] = 999;         // 战神之心掉落 +999个
         currentObj["lottery"] = 999;                 // 幸运值 999
         currentObj["coinMul"] = 999;                 // 商运掉率 +99900%
         currentObj["arenaStampDropNum"] = 999;       // 优胜券获取 +999个
         currentObj["vehicleCashDropNum"] = 999;      // 载具碎片掉落 +999个
         currentObj["lifeCatalystDropPro"] = 999;     // 生命催化剂掉率 +99900%
         currentObj["godStoneDropPro"] = 999;         // 神能石掉率 +99900%
         currentObj["converStoneDropPro"] = 999;      // 转化石掉率 +99900%
         currentObj["taxStampDropPro"] = 999;         // 化石掉率 +99900%
         currentObj["bloodStoneDropPro"] = 999;       // 血手掉率 +99900%
         currentObj["deviceDropPro"] = 999;           // 装置掉率 +99900%
         currentObj["loveAdd"] = 999;                 // 赠礼好感度 +999点
         currentObj["dayLoveAdd"] = 999;              // 好感度每天 +999点
         currentObj["dpsAll"] = 0.15;                 // 战斗力/神级 15%
         currentObj["bulletDedut"] = 0.39;            // 防弹值 39%
         currentObj["sweepingNum"] = 999;             // 每日扫荡次数 +999次

         // 额外推荐属性
         currentObj["exp"] = 999;                     // 经验获取 +99900%
         currentObj["expVip"] = 999;                  // VIP经验获取 +99900%
         currentObj["weaponDropPro"] = 999;           // 武器碎片掉率 +99900%
         currentObj["blackEquipDropPro"] = 999;       // 装备碎片掉率 +99900%
         currentObj["rareEquipDropPro"] = 999;        // 稀有装备掉率 +99900%
         currentObj["specialPartsDropPro"] = 999;     // 特殊零件掉率 +99900%
         currentObj["gemDropPro"] = 999;              // 宝石掉率 +99900%
         currentObj["petBookDropPro"] = 999;          // 尸宠图鉴掉率 +99900%
         currentObj["rareGeneDropPro"] = 999;         // 红橙基因体掉率 +99900%

         // 重新设置obj以确保保存
         s0.obj = currentObj;

         // 调试信息
         var debugText:String = "属性添加调试信息:\n";
         debugText += "obj是否存在: " + (s0.obj != null) + "\n";
         debugText += "obj属性数量: " + this.getObjectPropertyCount(s0.obj) + "\n";
         debugText += "无双水晶值: " + s0.obj["demStroneDropNum"] + "\n";
         debugText += "万能球值: " + s0.obj["demBallDropNum"] + "\n";
         debugText += "幸运值: " + s0.obj["lottery"] + "\n";

         // 检查装备是否已穿戴，如果是则刷新属性
         var successText:String = "已添加超级属性包！包含所有掉落属性和特殊属性！\n\n" + debugText;
         if(this.nowData && this.nowData.placeType == "wear")
         {
            this.nowData.normalPlayerData.fleshAllByEquip();
            successText += "\n\n✅ 装备已穿戴，属性立即生效！";
         }
         else
         {
            successText += "\n\n⚠️ 请重新穿戴装备以使属性生效！";
         }

         Gaming.uiGroup.alertBox.showSuccess(successText);
      }

      // 获取对象属性数量的辅助方法
      private function getObjectPropertyCount(obj:Object) : int
      {
         var count:int = 0;
         for(var prop:String in obj)
         {
            count++;
         }
         return count;
      }

      // 测试属性显示和生效
      private function testPropertyDisplay(s0:EquipSave) : void
      {
         // 获取当前obj的副本
         var currentObj:Object = s0.obj;
         if(!currentObj) currentObj = {};

         // 添加一些已知的标准属性进行测试
         currentObj["dps"] = 999;                    // 基础伤害
         currentObj["life"] = 999;                   // 生命值
         currentObj["demStroneDropNum"] = 999;       // 无双水晶掉落
         currentObj["lottery"] = 999;                // 幸运值
         currentObj["coinMul"] = 999;                // 商运掉率

         // 重新设置obj
         s0.obj = currentObj;

         // 获取显示文本进行调试
         var trueObj:Object = s0.getTrueObj();

         // 检查obj内容
         var objInfo:String = "📦 obj内容:\n";
         for(var prop:String in currentObj)
         {
            objInfo += "  " + prop + ": " + currentObj[prop] + "\n";
         }

         // 检查trueObj内容
         var trueObjInfo:String = "🔍 trueObj内容:\n";
         for(var prop2:String in trueObj)
         {
            trueObjInfo += "  " + prop2 + ": " + trueObj[prop2] + "\n";
         }

         // 检查属性是否生效
         var effectInfo:String = "⚡ 属性生效测试:\n";
         if(this.nowData && this.nowData.placeType == "wear")
         {
            // 获取玩家当前的掉落属性（不使用类型声明避免编译错误）
            var dropMerge:* = this.nowData.normalPlayerData.getDropMerge();
            effectInfo += "  当前无双水晶掉落: " + dropMerge.demStroneDropNum + "\n";
            effectInfo += "  当前万能球掉落: " + dropMerge.demBallDropNum + "\n";
            effectInfo += "  当前幸运值: " + dropMerge.lottery + "\n";
            effectInfo += "  当前商运掉率: " + dropMerge.coinMul + "\n";
            effectInfo += "  ✅ 装备已穿戴，属性应该生效！";
         }
         else
         {
            effectInfo += "  ⚠️ 装备未穿戴，属性不会生效！";
         }

         Gaming.uiGroup.alertBox.showSuccess("🧪 测试属性完成！\n\n" + objInfo + "\n" + trueObjInfo + "\n" + effectInfo);
      }

      // 清空所有属性
      private function clearAllProperties(s0:EquipSave) : void
      {
         s0.obj = {};
         Gaming.uiGroup.alertBox.showSuccess("已清空所有装备属性！");
      }

      // 处理掉落属性
      private function handleDropProperties(str0:String) : void
      {
         var s0:EquipSave = new EquipSave();
         s0.inData_byObj(this.nowData.save);

         // 获取当前obj的副本
         var currentObj:Object = s0.obj;
         if(!currentObj) currentObj = {};

         var ArrNum:Array = str0.split("&");
         var ArrNow:Array;
         var EquipNow:String;
         var addedCount:int = 0;

         for each(EquipNow in ArrNum)
         {
            ArrNow = EquipNow.split("*");
            var code:String = ArrNow[0];
            var value:Number = Number(ArrNow[1]);

            switch(code)
            {
               // 特殊掉落
               case "30": currentObj["demStroneDropNum"] = value; addedCount++; break;        // 无双水晶
               case "31": currentObj["demBallDropNum"] = value; addedCount++; break;          // 万能球
               case "32": currentObj["madheartDropNum"] = value; addedCount++; break;         // 战神之心
               case "33": currentObj["arenaStampDropNum"] = value; addedCount++; break;       // 优胜券
               case "34": currentObj["vehicleCashDropNum"] = value; addedCount++; break;      // 载具碎片

               // 材料掉落
               case "35": currentObj["lifeCatalystDropPro"] = value; addedCount++; break;     // 生命催化剂
               case "36": currentObj["godStoneDropPro"] = value; addedCount++; break;         // 神能石
               case "37": currentObj["converStoneDropPro"] = value; addedCount++; break;      // 转化石
               case "38": currentObj["taxStampDropPro"] = value; addedCount++; break;         // 化石
               case "39": currentObj["bloodStoneDropPro"] = value; addedCount++; break;       // 血手
               case "40": currentObj["deviceDropPro"] = value; addedCount++; break;           // 装置

               // 装备掉落
               case "41": currentObj["weaponDropPro"] = value; addedCount++; break;           // 武器碎片
               case "42": currentObj["blackEquipDropPro"] = value; addedCount++; break;       // 装备碎片
               case "43": currentObj["rareEquipDropPro"] = value; addedCount++; break;        // 稀有装备
               case "44": currentObj["specialPartsDropPro"] = value; addedCount++; break;     // 特殊零件
               case "45": currentObj["gemDropPro"] = value; addedCount++; break;              // 宝石
               case "46": currentObj["rareGeneDropPro"] = value; addedCount++; break;         // 基因体

               // 经验金币
               case "47": currentObj["exp"] = value; addedCount++; break;                     // 经验获取
               case "48": currentObj["expVip"] = value; addedCount++; break;                  // VIP经验
               case "49": currentObj["coinMul"] = value; addedCount++; break;                 // 银币获取
               case "50": currentObj["lottery"] = value; addedCount++; break;                 // 幸运值
            }
         }

         // 重新设置obj以确保保存
         s0.obj = currentObj;

         this.nowData.save = s0;
         this.showOneEquipDataAndPan(this.nowData);

         // 强制刷新装备属性合并
         if(this.nowData.placeType == "wear")
         {
            this.nowData.normalPlayerData.fleshAllByEquip();
            Gaming.uiGroup.alertBox.showSuccess("掉落属性编辑成功！已添加 " + addedCount + " 个属性！属性已生效！");
         }
         else
         {
            Gaming.uiGroup.alertBox.showSuccess("掉落属性编辑成功！已添加 " + addedCount + " 个属性！请重新穿戴装备以生效！");
         }
      }

      // 处理战斗属性
      private function handleCombatProperties(str0:String) : void
      {
         var s0:EquipSave = new EquipSave();
         s0.inData_byObj(this.nowData.save);
         if(!s0.obj) s0.obj = {};

         var ArrNum:Array = str0.split("&");
         var ArrNow:Array;
         var EquipNow:String;
         var addedCount:int = 0;

         for each(EquipNow in ArrNum)
         {
            ArrNow = EquipNow.split("*");
            var code:String = ArrNow[0];
            var value:Number = Number(ArrNow[1]);

            switch(code)
            {
               // 伤害属性
               case "60": s0.obj["dps"] = value; addedCount++; break;                     // 基础伤害
               case "61": s0.obj["dpsMul"] = value; addedCount++; break;                  // 伤害倍率
               case "62": s0.obj["dpsAll"] = value / 100; addedCount++; break;            // 全伤害 (转换为小数)
               case "63": s0.obj["critPro3"] = value / 100; addedCount++; break;          // 暴击概率
               case "64": s0.obj["attackGap"] = value; addedCount++; break;               // 攻击间隔

               // 生存属性
               case "65": s0.obj["life"] = value; addedCount++; break;                    // 生命值
               case "66": s0.obj["lifeMul"] = value; addedCount++; break;                 // 生命倍率
               case "67": s0.obj["lifeRate"] = value; addedCount++; break;                // 生命恢复
               case "68": s0.obj["head"] = value; addedCount++; break;                    // 头部防护
               case "69": s0.obj["dodge"] = value / 100; addedCount++; break;             // 闪避

               // 防御属性
               case "70": s0.obj["fightDedut"] = value / 100; addedCount++; break;        // 格斗减伤
               case "71": s0.obj["bulletDedut"] = value / 100; addedCount++; break;       // 防弹值
               case "72": s0.obj["skillDedut"] = value / 100; addedCount++; break;        // 技能减伤

               // 武器属性
               case "73": s0.obj["capacity"] = value; addedCount++; break;                // 弹夹容量
               case "74": s0.obj["charger"] = value; addedCount++; break;                 // 充能速度
               case "75": s0.obj["reload"] = value; addedCount++; break;                  // 换弹速度

               // 移动属性
               case "76": s0.obj["moveMul"] = value; addedCount++; break;                 // 移动速度
               case "77": s0.obj["maxJumpNumAdd"] = value; addedCount++; break;           // 跳跃次数
            }
         }

         this.nowData.save = s0;
         this.showOneEquipDataAndPan(this.nowData);
         Gaming.uiGroup.alertBox.showSuccess("战斗属性编辑成功！已添加 " + addedCount + " 个属性！");
      }

      // 处理特殊属性
      private function handleSpecialProperties(str0:String) : void
      {
         var s0:EquipSave = new EquipSave();
         s0.inData_byObj(this.nowData.save);
         if(!s0.obj) s0.obj = {};

         var ArrNum:Array = str0.split("&");
         var ArrNow:Array;
         var EquipNow:String;
         var addedCount:int = 0;

         for each(EquipNow in ArrNum)
         {
            ArrNow = EquipNow.split("*");
            var code:String = ArrNow[0];
            var value:Number = Number(ArrNow[1]);

            switch(code)
            {
               // 好感度属性
               case "80": s0.obj["loveAdd"] = value; addedCount++; break;                 // 赠礼好感度
               case "81": s0.obj["dayLoveAdd"] = value; addedCount++; break;              // 每日好感度

               // 功能属性
               case "82": s0.obj["sweepingNum"] = value; addedCount++; break;             // 每日扫荡次数

               // 载具属性
               case "83": s0.obj["vehicleDefMul"] = value; addedCount++; break;           // 载具防御
               case "84": s0.obj["vehicleDpsMul"] = value; addedCount++; break;           // 载具伤害

               // 队友属性
               case "85": s0.obj["moreDpsMul"] = value; addedCount++; break;              // 队友伤害
               case "86": s0.obj["moreLifeMul"] = value; addedCount++; break;             // 队友生命

               // 特殊效果
               case "87": s0.obj["damageMul"] = value; addedCount++; break;               // 伤害倍增器
               case "88": s0.obj["cdMul"] = value / 100; addedCount++; break;             // 冷却缩减
            }
         }

         this.nowData.save = s0;
         this.showOneEquipDataAndPan(this.nowData);
         Gaming.uiGroup.alertBox.showSuccess("特殊属性编辑成功！已添加 " + addedCount + " 个属性！");
      }
   }
}

