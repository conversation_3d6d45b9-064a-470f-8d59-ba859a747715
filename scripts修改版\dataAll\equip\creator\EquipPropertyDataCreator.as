package dataAll.equip.creator
{
   import com.common.data.Base64;
   import com.sounto.oldUtils.ComMethod;
   import dataAll.arms.define.ArmsChargerDefine;
   import dataAll.arms.define.ArmsType;
   import dataAll.drop.define.EquipColorDefine;
   import dataAll.equip.EquipPropertyData;
   import dataAll.equip.define.EquipColor;
   import dataAll.equip.define.EquipType;
   import dataAll.equip.save.EquipSave;
   import dataAll.pro.ProType;
   import dataAll.pro.PropertyArrayDefine;
   import dataAll.skill.define.SkillDescrip;
   
   public class EquipPropertyDataCreator
   {
      
      private static var propertyObj:Object = {};
      
      private static var propertyBase64:String = "";
      
      private static var normalPropertyArr:Array = [];
      
      private static var specialPropertyArr:Array = [];
      
      private static var redPropertyArr:Array = [];
      
      private static var allPropertyArr:Array = [];
      
      private static var otherPropertyArr:Array = [];
      
      public var allGroupArr:Array;
      
      public var dpsGroupArr:Array;
      
      public var bulletGroupArr:Array;
      
      public var lifeGroupArr:Array;
      
      public var gainGroupArr:Array;
      
      public var haveTypeGroupArr:Array;
      
      public var normalGroupArr:Array;
      
      public var specialGroupArr:Array;
      
      public var headNoArr:Array;
      
      public var coatNoArr:Array;
      
      public var pantsNoArr:Array;
      
      public var beltNoArr:Array;
      
      public var normalObj:Object;
      
      public var specialObj:Object;
      
      private var redObj:Object;
      
      private var armsTypePer:Number = 0;
      
      private var armsTypeMul:Number = 0.6;
      
      public function EquipPropertyDataCreator()
      {
         var n:* = undefined;
         var type0:String = null;
         this.allGroupArr = [];
         this.dpsGroupArr = ["dps","dpsMul","hurt","hurtMul"];
         this.bulletGroupArr = ["capacity","charger","reload","capacityMul","chargerMul"];
         this.lifeGroupArr = ["life","lifeMul","lifeRate","head","headMul"];
         this.gainGroupArr = ["coinMul","expMul"];
         this.haveTypeGroupArr = [];
         this.normalGroupArr = ["capacity","charger","life","head","dps"];
         this.specialGroupArr = [];
         this.headNoArr = [];
         this.coatNoArr = [];
         this.pantsNoArr = [];
         this.beltNoArr = [];
         this.normalObj = {};
         this.specialObj = {};
         this.redObj = {
            "head":this.gainGroupArr.concat([]),
            "coat":this.dpsGroupArr.concat([]),
            "pants":this.dpsGroupArr.concat([]),
            "belt":this.bulletGroupArr.concat([])
         };
         super();
         this.allGroupArr = this.dpsGroupArr.concat(this.bulletGroupArr).concat(this.lifeGroupArr);
         this.specialGroupArr = this.minusArray(this.allGroupArr,this.normalGroupArr);
         this.haveTypeGroupArr = this.dpsGroupArr.concat(this.bulletGroupArr);
         this.headNoArr = this.bulletGroupArr.concat(this.dpsGroupArr).concat(this.gainGroupArr);
         this.coatNoArr = this.bulletGroupArr.concat(["head","headMul"]);
         this.pantsNoArr = this.coatNoArr;
         this.beltNoArr = this.dpsGroupArr.concat(this.lifeGroupArr);
         var partArr0:Array = EquipType.NORMAL_ARR;
         for(n in partArr0)
         {
            type0 = partArr0[n];
            this.normalObj[type0] = this.minusArray(this.normalGroupArr,this[type0 + "NoArr"]);
            this.specialObj[type0] = this.minusArray(this.specialGroupArr,this[type0 + "NoArr"]);
         }
      }
      
      public static function getPropertyArrayDefine(name0:String) : PropertyArrayDefine
      {
         return propertyObj[name0];
      }
      
      public static function getProCnArr(proArr0:Array) : Array
      {
         var pro0:String = null;
         var d0:PropertyArrayDefine = null;
         var arr0:Array = [];
         for each(pro0 in proArr0)
         {
            d0 = EquipPropertyDataCreator.getPropertyArrayDefine(pro0);
            if(Boolean(d0))
            {
               arr0.push(d0.cnName);
            }
            else
            {
               arr0.push("未找到");
            }
         }
         return arr0;
      }
      
      public static function getLvInOnePro(v0:Number, proName0:String) : int
      {
         var v2:Number = NaN;
         var bigNum0:int = 0;
         for(var i:int = 1; i < 200; i++)
         {
            v2 = getValueByPro(proName0,i);
            if(v2 > v0)
            {
               bigNum0++;
               if(bigNum0 >= 2)
               {
                  return i - 1;
               }
            }
         }
         return 200;
      }
      
      public static function getValueByPro(name0:String, lv0:int) : Number
      {
         var d0:PropertyArrayDefine = null;
         if(propertyObj.hasOwnProperty(name0))
         {
            d0 = propertyObj[name0];
            return d0.getValue(lv0);
         }
         INIT.showError("找不到定义PropertyArrayDefine：" + name0);
         return 0;
      }
      
      public static function getText_byObj(obj0:Object, compareObj0:Object = null, allProB0:Boolean = false, lastFun0:Function = null) : String
      {
         var str0:String = "";
         str0 += getTextRange(obj0,normalPropertyArr,"gray",compareObj0,false,lastFun0);
         str0 += getTextRange(obj0,specialPropertyArr,"yellow",compareObj0,false,lastFun0);
         str0 += getTextRange(obj0,redPropertyArr,"redness2",compareObj0,false,lastFun0);
         if(allProB0)
         {
            str0 += getTextRange(obj0,otherPropertyArr,"redness2",compareObj0,false,lastFun0);
         }

         // 添加扩展属性显示支持
         str0 += getSpecialDropText(obj0, compareObj0);

         return str0;
      }
      
      public static function getText_byObjNoColor(obj0:Object, compareObj0:Object = null, allProB0:Boolean = false, lastFun0:Function = null) : String
      {
         var str0:String = "";
         str0 += getTextRange(obj0,normalPropertyArr,"gray",compareObj0,false,lastFun0);
         str0 += getTextRange(obj0,specialPropertyArr,"gray",compareObj0,false,lastFun0);
         str0 += getTextRange(obj0,redPropertyArr,"gray",compareObj0,false,lastFun0);
         if(allProB0)
         {
            str0 += getTextRange(obj0,otherPropertyArr,"gray",compareObj0,false,lastFun0);
         }

         // 添加扩展属性显示支持（灰色模式）
         str0 += getSpecialDropTextNoColor(obj0, compareObj0);

         return str0;
      }
      
      public static function getTextRange(obj0:Object, arr0:Array, color0:String = "gray", compareObj0:Object = null, moreZeroB0:Boolean = false, lastFun0:Function = null) : String
      {
         var d0:PropertyArrayDefine = null;
         var n:String = null;
         var s0:String = null;
         var str0:String = "";
         for each(d0 in arr0)
         {
            n = d0.name;
            if(obj0.hasOwnProperty(n))
            {
               s0 = getOneTextRange(d0,obj0[n],Boolean(compareObj0) ? compareObj0[n] : null,moreZeroB0,color0);
               if(s0 != "")
               {
                  if(lastFun0 is Function)
                  {
                     s0 = lastFun0(d0,s0,obj0[n]);
                  }
                  str0 += s0 + "\n";
               }
            }
         }
         return str0;
      }
      
      public static function getOneTextRange(d0:PropertyArrayDefine, value0:*, compareValue0:* = null, moreZeroB0:Boolean = false, color0:String = "gray", replaceValueStr0:String = "") : String
      {
         var v0:Number = NaN;
         var v2:Number = NaN;
         var bb0:Boolean = false;
         var arr0:Array = null;
         if(color0 == "")
         {
            color0 = d0.gatherColor;
         }
         var v_color0:String = color0;
         var valueStr0:String = "";
         var lastIcon0:String = "";
         if(value0 is Number)
         {
            v0 = value0;
            if(moreZeroB0 && v0 == 0)
            {
               return "";
            }
            valueStr0 = d0.getValueString(v0);
            if(compareValue0 != null)
            {
               v2 = compareValue0;
               if(v0 > v2)
               {
                  lastIcon0 = "|<i6>";
               }
               else if(v0 < v2)
               {
                  lastIcon0 = "|<i7>";
               }
            }
         }
         else if(value0 is Boolean)
         {
            bb0 = value0;
            if(moreZeroB0 && bb0 == false)
            {
               return "";
            }
            valueStr0 = ProType.booleanToUI(bb0);
         }
         else if(value0 is String)
         {
            if(value0 == "")
            {
               return "";
            }
            if(d0.child == ProType.itemsColor)
            {
               valueStr0 = EquipColor.getColorCn(value0 as String);
            }
         }
         else if(value0 is Array)
         {
            arr0 = value0 as Array;
            if(arr0.length == 0)
            {
               return "";
            }
            if(d0.child == ProType.skill)
            {
               valueStr0 = SkillDescrip.getCnText(arr0);
            }
         }
         var vs0:String = "<" + v_color0 + " " + valueStr0 + "/>";
         if(replaceValueStr0 != "")
         {
            vs0 = replaceValueStr0;
         }
         return "<" + color0 + " " + d0.cnName + "/>|" + vs0 + lastIcon0;
      }
      
      public function test1() : void
      {
         for(var i:int = 1; i <= 50; i++)
         {
         }
      }
      
      public function inData_byXML(xml0:XML) : void
      {
         var n:* = undefined;
         var pro_xml0:XML = null;
         var d0:PropertyArrayDefine = null;
         var i:* = undefined;
         var type0:String = null;
         var type_d0:ArmsChargerDefine = null;
         var cnType0:String = null;
         var d1:PropertyArrayDefine = null;
         var typeProMul0:Number = NaN;
         var typeProName0:String = null;
         var armsTypeMul0:Number = NaN;
         var d1_dataArr0:Array = null;
         var j:* = undefined;
         var proArr0:XMLList = xml0.pro;
         var typeArr0:Array = Gaming.defineGroup.armsCharger.typeArr;
         for(n in proArr0)
         {
            pro_xml0 = proArr0[n];
            d0 = new PropertyArrayDefine();
            d0.inData_byXML(pro_xml0);
            propertyObj[d0.name] = d0;
            if(this.normalGroupArr.indexOf(d0.name) >= 0)
            {
               normalPropertyArr.push(d0);
            }
            else if(this.specialGroupArr.indexOf(d0.name) >= 0)
            {
               specialPropertyArr.push(d0);
            }
            else
            {
               redPropertyArr.push(d0);
            }
            if(this.haveTypeGroupArr.indexOf(d0.name) >= 0)
            {
               for(i in typeArr0)
               {
                  type0 = typeArr0[i];
                  type_d0 = Gaming.defineGroup.armsCharger.getDefine(type0);
                  if(!type_d0)
                  {
                     INIT.showError("不存在ArmsChargerDefine：" + type0);
                  }
                  cnType0 = type_d0.cnName;
                  d1 = new PropertyArrayDefine();
                  d1.name = d0.name + "_" + type0;
                  d1.unit = d0.unit;
                  d1.cnName = d0.cnName + "/" + cnType0.substr(0,2);
                  typeProMul0 = 1;
                  typeProName0 = d0.name + "Mul";
                  if(type_d0.hasOwnProperty(typeProName0))
                  {
                     typeProMul0 = Number(type_d0[typeProName0]);
                  }
                  armsTypeMul0 = 0.6;
                  if(d1.name.indexOf("Mul") > 0)
                  {
                     armsTypeMul0 = 0.3;
                  }
                  d1_dataArr0 = [];
                  for(j in d0.dataArr)
                  {
                     d1_dataArr0[j] = d0.fixedNumber(Number(d0.dataArr[j] * armsTypeMul0 * typeProMul0));
                  }
                  d1.dataArr = d1_dataArr0;
                  propertyObj[d1.name] = d1;
                  redPropertyArr.push(d1);
               }
            }
         }
         propertyBase64 = Base64.encodeObject(propertyObj);
         this.inAllProArr();
      }
      
      public function inRedProperty(arr0:Array) : void
      {
      }
      
      private function inAllProArr() : void
      {
         var name0:String = null;
         var d0:PropertyArrayDefine = null;
         var arr0:Array = EquipPropertyData.pro_arr.concat([]);
         for each(name0 in arr0)
         {
            d0 = Gaming.defineGroup.getPropertyArrayDefine(name0);
            if(Boolean(d0))
            {
               allPropertyArr.push(d0);
            }
         }
         otherPropertyArr = ComMethod.deductArr(allPropertyArr,normalPropertyArr);
         otherPropertyArr = ComMethod.deductArr(otherPropertyArr,specialPropertyArr);
         otherPropertyArr = ComMethod.deductArr(otherPropertyArr,redPropertyArr);
      }
      
      public function zuobiPan() : String
      {
         var str2:String = Base64.encodeObject(propertyObj);
         if(propertyBase64 != str2)
         {
            return "修改了装备固定数值";
         }
         return "";
      }
      
      public function getObj(color0:String, type0:String, lv0:int, dropName0:String) : Object
      {
         var cd0:EquipColorDefine = Gaming.defineGroup.dropColor.getByName(dropName0).equip;
         var index0:int = int(cd0.name.indexOf(color0));
         var n_num:int = int(cd0.normalNum[index0]);
         var s_num:int = int(cd0.specialNum[index0]);
         var redNum0:int = int(cd0.redNum[index0]);
         var lv_range:Array = cd0.lvRange[index0];
         return this.getObj_byNum(type0,n_num,s_num,redNum0,lv_range,lv0);
      }
      
      public function getObj_byNum(type0:String, n_num:int, s_num:int, redNum0:int, lv_range:Array, lv0:int) : Object
      {
         var arr2:Array = null;
         var obj2:Object = null;
         var redProArr0:Array = null;
         var redArr0:Array = null;
         var redObj0:Object = null;
         var normalProArr0:Array = this.normalObj[type0];
         var specialProArr0:Array = this.specialObj[type0];
         var arr1:Array = this.getRandomProArr(n_num,normalProArr0);
         var obj1:Object = this.getRandomValueObj(arr1,lv_range,lv0);
         if(s_num > 0)
         {
            arr2 = this.getRandomProArr(s_num,specialProArr0);
            obj2 = this.getRandomValueObj(arr2,lv_range,lv0);
            this.plusObj(obj1,obj2);
         }
         if(redNum0 > 0)
         {
            redProArr0 = this.redObj[type0];
            redArr0 = this.getRandomProArr(redNum0,redProArr0,true);
            redObj0 = this.getRandomValueObj(redArr0,lv_range,lv0);
            this.plusObj(obj1,redObj0);
         }
         return obj1;
      }
      
      public function getSuperObj(color0:String, type0:String, lv0:int, addArmsType0:String = "") : Object
      {
         var proArr0:Array = null;
         if(addArmsType0 == "")
         {
            addArmsType0 = ArmsType.pistol;
         }
         var cd0:EquipColorDefine = Gaming.defineGroup.dropColor.getByName("diff_3").equip;
         var index0:int = int(cd0.name.indexOf(color0));
         var n_num:int = int(cd0.normalNum[index0]);
         var s_num:int = int(cd0.specialNum[index0]);
         var lv_range:Array = [0,0];
         var obj0:Object = this.getObj_byNum(type0,n_num,s_num,0,lv_range,lv0);
         if(type0 == "coat" || type0 == "pants")
         {
            proArr0 = ["lifeMul","dps","dpsMul","hurt","hurtMul","dpsMul_" + addArmsType0];
            obj0 = this.getRandomValueObj(proArr0,lv_range,lv0);
         }
         else if(type0 == "head")
         {
            obj0["coinMul"] = this.getValue_byLevel("coinMul",lv0 + 1);
         }
         else
         {
            obj0["capacityMul_" + addArmsType0] = 0.14;
         }
         return obj0;
      }
      
      public function getBlackNormalProArr(type0:String) : Array
      {
         if(type0 == EquipType.COAT || type0 == EquipType.PANTS)
         {
            return ["lifeMul","dps","dpsMul","hurt","hurtMul"];
         }
         if(type0 == EquipType.HEAD)
         {
            return this.lifeGroupArr;
         }
         if(type0 == EquipType.BELT)
         {
            return this.bulletGroupArr;
         }
         return [];
      }
      
      private function getRandomProArr(num0:int, rangeArr0:Array, mustArmsTypeB0:Boolean = false) : Array
      {
         var index0:int = 0;
         var pro0:String = null;
         var arr0:Array = rangeArr0.concat([]);
         var proArr0:Array = [];
         var armsTypeArr0:Array = Gaming.defineGroup.armsCharger.joinPropertyArr;
         for(var i:int = 0; i < num0; i++)
         {
            if(arr0.length == 0)
            {
               break;
            }
            index0 = int(Math.random() * arr0.length);
            pro0 = arr0[index0];
            arr0.splice(index0,1);
            if(this.haveTypeGroupArr.indexOf(pro0) >= 0 && mustArmsTypeB0)
            {
               pro0 = pro0 + "_" + armsTypeArr0[int(Math.random() * armsTypeArr0.length)];
            }
            proArr0.push(pro0);
         }
         return proArr0;
      }
      
      private function minusArray(arr0:Array, arr1:Array) : Array
      {
         var n:* = undefined;
         var str0:String = null;
         var arr2:Array = [];
         for(n in arr0)
         {
            str0 = arr0[n];
            if(arr1.indexOf(str0) == -1)
            {
               arr2.push(str0);
            }
         }
         return arr2;
      }
      
      public function randomBlackValue(obj0:Object) : void
      {
         var n:* = undefined;
         var d0:PropertyArrayDefine = null;
         var v0:Number = NaN;
         var range0:Array = null;
         for(n in obj0)
         {
            d0 = Gaming.defineGroup.getPropertyArrayDefine(n);
            v0 = Number(obj0[n]);
            range0 = d0.getBlackValueRange(v0);
            v0 = Math.random() * (range0[1] - range0[0]) + range0[0];
            v0 = d0.fixedNumber(v0);
            obj0[n] = v0;
         }
      }
      
      public function getRandomValueObj(proArr0:Array, lv_range:Array, lv0:int) : Object
      {
         var n:* = undefined;
         var name0:String = null;
         var minLv0:int = 0;
         var maxLv0:int = 0;
         var max_v0:Number = NaN;
         var min_v0:Number = NaN;
         var v0:Number = NaN;
         var d0:PropertyArrayDefine = null;
         var obj0:Object = {};
         for(n in proArr0)
         {
            name0 = proArr0[n];
            minLv0 = lv0 + lv_range[0];
            maxLv0 = lv0 + lv_range[1];
            max_v0 = this.getValue_byLevel(name0,maxLv0);
            min_v0 = this.getValue_byLevel(name0,minLv0);
            if(minLv0 < 1 && min_v0 >= 2)
            {
               min_v0 /= 2;
            }
            v0 = min_v0 + Math.random() * (max_v0 - min_v0);
            d0 = propertyObj[name0];
            if(Boolean(d0))
            {
               v0 = d0.fixedNumber(v0);
            }
            obj0[name0] = v0;
         }
         return obj0;
      }
      
      private function getValue_byLevel(pro0:String, lv0:int) : Number
      {
         var pro1:String = pro0;
         var d0:PropertyArrayDefine = propertyObj[pro1];
         if(!(d0 is PropertyArrayDefine))
         {
            return 0;
         }
         var dataArr0:Array = d0.dataArr;
         var value0:Number = 0;
         if(!dataArr0)
         {
            value0 = 0;
         }
         else if(dataArr0.length == 0)
         {
            value0 = 0;
         }
         else if(lv0 < 1)
         {
            value0 = Number(dataArr0[0]);
         }
         else if(lv0 > dataArr0.length)
         {
            value0 = Number(dataArr0[dataArr0.length - 1]);
         }
         else
         {
            value0 = Number(dataArr0[lv0 - 1]);
         }
         if(pro0 != pro1)
         {
            value0 *= this.armsTypeMul;
         }
         return d0.fixedNumber(value0);
      }
      
      private function plusObj(obj1:Object, obj2:Object) : void
      {
         var n:* = undefined;
         for(n in obj2)
         {
            obj1[n] = obj2[n];
         }
      }
      
      public function isAllHurtProB(obj0:Object) : Boolean
      {
         var pro0:String = null;
         for each(pro0 in this.dpsGroupArr)
         {
            if(!obj0.hasOwnProperty(pro0))
            {
               return false;
            }
         }
         return true;
      }
      
      public function repairObj(obj0:Object, lv0:int) : Object
      {
         var n:* = undefined;
         var f0:int = 0;
         var n2:String = null;
         var d0:PropertyArrayDefine = null;
         var max0:Number = NaN;
         var v2:Number = NaN;
         var obj2:Object = {};
         for(n in obj0)
         {
            f0 = int(n.indexOf("_"));
            if(f0 > 0)
            {
               n2 = String(n).substr(0,f0);
               d0 = getPropertyArrayDefine(n2);
               max0 = d0.getValue(lv0);
               v2 = d0.fixedNumber(obj0[n] / 2);
               if(v2 > max0)
               {
                  v2 = max0;
               }
               obj2[n2] = v2;
            }
            else
            {
               obj2[n] = obj0[n];
            }
         }
         return obj2;
      }
      
      private function getProMax(name0:String, lv0:int) : Number
      {
         var max0:Number = this.getValue_byLevel(name0,lv0);
         if(max0 <= 0.01)
         {
            return Gaming.defineGroup.equip.getBlackProMax(name0);
         }
         return max0;
      }
      
      public function zuobiPanEquipSave(s0:EquipSave) : String
      {
         var n:* = undefined;
         var lv0:int = 0;
         var max0:Number = NaN;
         var obj0:Object = s0.obj;
         if(obj0 is Object)
         {
            for(n in obj0)
            {
               lv0 = s0.getTrueLevel();
               if(lv0 < 30)
               {
                  lv0 = 30;
               }
               max0 = this.getValue_byLevel(n,lv0);
               if(max0 > 0.01)
               {
                  if(obj0[n] > max0 * 5)
                  {
                     return n + "：" + obj0[n] + "》" + max0 * 5;
                  }
               }
            }
         }
         return "";
      }
      
      public function zuobiPanBlackEquipSave(s0:EquipSave) : String
      {
         var n:* = undefined;
         var lv0:int = 0;
         var max0:Number = NaN;
         var obj0:Object = s0.obj;
         var colorIndex0:int = EquipColor.getIndex(s0.color);
         if(colorIndex0 <= 6)
         {
            if(obj0 is Object)
            {
               for(n in obj0)
               {
                  lv0 = s0.getTrueLevel();
                  if(lv0 < 20)
                  {
                     lv0 = 20;
                  }
                  max0 = Gaming.defineGroup.equip.getBlackProMax(n);
                  if(max0 > 0.01)
                  {
                     if(obj0[n] > max0)
                     {
                        return "黑色装备 " + n + "：" + obj0[n] + "》" + max0;
                     }
                  }
               }
            }
         }
         return "";
      }

      // 获取特殊掉落属性显示文本（彩色版本）
      public static function getSpecialDropText(obj0:Object, compareObj0:Object = null) : String
      {
         var str0:String = "";

         // 特殊掉落属性
         if(obj0["demStroneDropNum"]) str0 += "\n<yellow 无双水晶掉落 +" + obj0["demStroneDropNum"] + "个/>";
         if(obj0["demBallDropNum"]) str0 += "\n<yellow 万能球掉落 +" + obj0["demBallDropNum"] + "个/>";
         if(obj0["madheartDropNum"]) str0 += "\n<yellow 战神之心掉落 +" + obj0["madheartDropNum"] + "个/>";
         if(obj0["arenaStampDropNum"]) str0 += "\n<yellow 优胜券获取 +" + obj0["arenaStampDropNum"] + "个/>";
         if(obj0["vehicleCashDropNum"]) str0 += "\n<yellow 载具碎片掉落 +" + obj0["vehicleCashDropNum"] + "个/>";

         // 材料掉落属性
         if(obj0["lifeCatalystDropPro"]) str0 += "\n<yellow 生命催化剂掉率 +" + (obj0["lifeCatalystDropPro"] * 100) + "%/>";
         if(obj0["godStoneDropPro"]) str0 += "\n<yellow 神能石掉率 +" + (obj0["godStoneDropPro"] * 100) + "%/>";
         if(obj0["converStoneDropPro"]) str0 += "\n<yellow 转化石掉率 +" + (obj0["converStoneDropPro"] * 100) + "%/>";
         if(obj0["taxStampDropPro"]) str0 += "\n<yellow 化石掉率 +" + (obj0["taxStampDropPro"] * 100) + "%/>";
         if(obj0["bloodStoneDropPro"]) str0 += "\n<yellow 血手掉率 +" + (obj0["bloodStoneDropPro"] * 100) + "%/>";
         if(obj0["deviceDropPro"]) str0 += "\n<yellow 装置掉率 +" + (obj0["deviceDropPro"] * 100) + "%/>";

         // 装备掉落属性
         if(obj0["weaponDropPro"]) str0 += "\n<yellow 武器碎片掉率 +" + (obj0["weaponDropPro"] * 100) + "%/>";
         if(obj0["blackEquipDropPro"]) str0 += "\n<yellow 装备碎片掉率 +" + (obj0["blackEquipDropPro"] * 100) + "%/>";
         if(obj0["rareEquipDropPro"]) str0 += "\n<yellow 稀有装备掉率 +" + (obj0["rareEquipDropPro"] * 100) + "%/>";
         if(obj0["specialPartsDropPro"]) str0 += "\n<yellow 特殊零件掉率 +" + (obj0["specialPartsDropPro"] * 100) + "%/>";
         if(obj0["gemDropPro"]) str0 += "\n<yellow 宝石掉率 +" + (obj0["gemDropPro"] * 100) + "%/>";
         if(obj0["rareGeneDropPro"]) str0 += "\n<yellow 基因体掉率 +" + (obj0["rareGeneDropPro"] * 100) + "%/>";

         // 经验金币属性
         if(obj0["exp"]) str0 += "\n<yellow 经验获取 +" + (obj0["exp"] * 100) + "%/>";
         if(obj0["expVip"]) str0 += "\n<yellow VIP经验获取 +" + (obj0["expVip"] * 100) + "%/>";
         if(obj0["coinMul"]) str0 += "\n<yellow 商运掉率 +" + (obj0["coinMul"] * 100) + "%/>";
         if(obj0["lottery"]) str0 += "\n<yellow 幸运值 " + obj0["lottery"] + "/>";

         // 好感度属性
         if(obj0["loveAdd"]) str0 += "\n<yellow 赠礼好感度 +" + obj0["loveAdd"] + "点/>";
         if(obj0["dayLoveAdd"]) str0 += "\n<yellow 好感度每天 +" + obj0["dayLoveAdd"] + "点/>";

         // 功能属性
         if(obj0["sweepingNum"]) str0 += "\n<yellow 每日扫荡次数 +" + obj0["sweepingNum"] + "次/>";

         return str0;
      }

      // 获取特殊掉落属性显示文本（灰色版本）
      public static function getSpecialDropTextNoColor(obj0:Object, compareObj0:Object = null) : String
      {
         var str0:String = "";

         // 特殊掉落属性
         if(obj0["demStroneDropNum"]) str0 += "\n<gray 无双水晶掉落 +" + obj0["demStroneDropNum"] + "个/>";
         if(obj0["demBallDropNum"]) str0 += "\n<gray 万能球掉落 +" + obj0["demBallDropNum"] + "个/>";
         if(obj0["madheartDropNum"]) str0 += "\n<gray 战神之心掉落 +" + obj0["madheartDropNum"] + "个/>";
         if(obj0["arenaStampDropNum"]) str0 += "\n<gray 优胜券获取 +" + obj0["arenaStampDropNum"] + "个/>";
         if(obj0["vehicleCashDropNum"]) str0 += "\n<gray 载具碎片掉落 +" + obj0["vehicleCashDropNum"] + "个/>";

         // 材料掉落属性
         if(obj0["lifeCatalystDropPro"]) str0 += "\n<gray 生命催化剂掉率 +" + (obj0["lifeCatalystDropPro"] * 100) + "%/>";
         if(obj0["godStoneDropPro"]) str0 += "\n<gray 神能石掉率 +" + (obj0["godStoneDropPro"] * 100) + "%/>";
         if(obj0["converStoneDropPro"]) str0 += "\n<gray 转化石掉率 +" + (obj0["converStoneDropPro"] * 100) + "%/>";
         if(obj0["taxStampDropPro"]) str0 += "\n<gray 化石掉率 +" + (obj0["taxStampDropPro"] * 100) + "%/>";
         if(obj0["bloodStoneDropPro"]) str0 += "\n<gray 血手掉率 +" + (obj0["bloodStoneDropPro"] * 100) + "%/>";
         if(obj0["deviceDropPro"]) str0 += "\n<gray 装置掉率 +" + (obj0["deviceDropPro"] * 100) + "%/>";

         // 装备掉落属性
         if(obj0["weaponDropPro"]) str0 += "\n<gray 武器碎片掉率 +" + (obj0["weaponDropPro"] * 100) + "%/>";
         if(obj0["blackEquipDropPro"]) str0 += "\n<gray 装备碎片掉率 +" + (obj0["blackEquipDropPro"] * 100) + "%/>";
         if(obj0["rareEquipDropPro"]) str0 += "\n<gray 稀有装备掉率 +" + (obj0["rareEquipDropPro"] * 100) + "%/>";
         if(obj0["specialPartsDropPro"]) str0 += "\n<gray 特殊零件掉率 +" + (obj0["specialPartsDropPro"] * 100) + "%/>";
         if(obj0["gemDropPro"]) str0 += "\n<gray 宝石掉率 +" + (obj0["gemDropPro"] * 100) + "%/>";
         if(obj0["rareGeneDropPro"]) str0 += "\n<gray 基因体掉率 +" + (obj0["rareGeneDropPro"] * 100) + "%/>";

         // 经验金币属性
         if(obj0["exp"]) str0 += "\n<gray 经验获取 +" + (obj0["exp"] * 100) + "%/>";
         if(obj0["expVip"]) str0 += "\n<gray VIP经验获取 +" + (obj0["expVip"] * 100) + "%/>";
         if(obj0["coinMul"]) str0 += "\n<gray 商运掉率 +" + (obj0["coinMul"] * 100) + "%/>";
         if(obj0["lottery"]) str0 += "\n<gray 幸运值 " + obj0["lottery"] + "/>";

         // 好感度属性
         if(obj0["loveAdd"]) str0 += "\n<gray 赠礼好感度 +" + obj0["loveAdd"] + "点/>";
         if(obj0["dayLoveAdd"]) str0 += "\n<gray 好感度每天 +" + obj0["dayLoveAdd"] + "点/>";

         // 功能属性
         if(obj0["sweepingNum"]) str0 += "\n<gray 每日扫荡次数 +" + obj0["sweepingNum"] + "次/>";

         return str0;
      }
   }
}

