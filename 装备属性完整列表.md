# 装备属性完整列表

## 概述

本文档列出了游戏中所有可以添加到装备上的属性，包括你提到的特殊属性以及更多可用属性。

## 🎯 你提到的属性列表

### ✅ 已确认可添加的属性

| 中文名称 | 英文属性名 | 数值类型 | 建议值 | 状态 |
|---------|-----------|---------|--------|------|
| 无双水晶掉落 | `demStroneDropNum` | 个数 | +999个 | ✅ 支持 |
| 万能球掉落 | `demBallDropNum` | 个数 | +999个 | ✅ 支持 |
| 战神之心掉落 | `madheartDropNum` | 个数 | +999个 | ✅ 支持 |
| 幸运值 | `lottery` | 数值 | 999 | ✅ 支持 |
| 商运掉率 | `coinMul` | 百分比 | +99900% | ✅ 支持 |
| 优胜券获取 | `arenaStampDropNum` | 个数 | +999个 | ✅ 支持 |
| 载具碎片掉落 | `vehicleCashDropNum` | 个数 | +999个 | ✅ 支持 |
| 生命催化剂掉率 | `lifeCatalystDropPro` | 百分比 | +99900% | ✅ 支持 |
| 神能石掉率 | `godStoneDropPro` | 百分比 | +99900% | ✅ 支持 |
| 转化石掉率 | `converStoneDropPro` | 百分比 | +99900% | ✅ 支持 |
| 化石掉率 | `taxStampDropPro` | 百分比 | +99900% | ✅ 支持 |
| 血手掉率 | `bloodStoneDropPro` | 百分比 | +99900% | ✅ 支持 |
| 装置掉率 | `deviceDropPro` | 百分比 | +99900% | ✅ 支持 |
| 赠礼好感度 | `loveAdd` | 点数 | +999点 | ✅ 支持 |
| 好感度每天 | `dayLoveAdd` | 点数 | +999点 | ✅ 支持 |
| 战斗力/神级 | `dpsAll` | 百分比 | 15% | ✅ 支持 |
| 防弹值 | `bulletDedut` | 百分比 | 39% | ✅ 支持 |
| 每日扫荡次数 | `sweepingNum` | 次数 | +999次 | ✅ 支持 |

## 🔍 更多可用属性

### 战斗属性 (DPS)
| 中文名称 | 英文属性名 | 说明 |
|---------|-----------|------|
| 攻击间隔 | `attackGap` | 减少攻击间隔 |
| 暴击概率 | `critPro3` | 暴击几率 |
| 整体伤害 | `dpsWhole` | 整体DPS加成 |
| 基础伤害 | `dps` | 基础伤害值 |
| 伤害倍率 | `dpsMul` | 伤害倍率 |
| 全伤害 | `dpsAll` | 全部伤害加成 |
| 黑色伤害 | `dpsAllBlack` | 黑色装备伤害 |
| VIP伤害 | `dpsVip` | VIP伤害加成 |
| 单发伤害 | `hurt` | 单发伤害 |
| 伤害倍增 | `hurtMul` | 伤害倍增 |
| 全伤害加成 | `hurtAll` | 全伤害加成 |

### 生存属性 (LIFE)
| 中文名称 | 英文属性名 | 说明 |
|---------|-----------|------|
| 生命值 | `life` | 基础生命值 |
| 生命倍率 | `lifeMul` | 生命值倍率 |
| 全生命 | `lifeAll` | 全生命加成 |
| 黑色生命 | `lifeAllBlack` | 黑色装备生命 |
| VIP生命 | `lifeVip` | VIP生命加成 |
| 生命恢复 | `lifeRate` | 生命恢复速度 |
| 黑色恢复 | `lifeRateBlack` | 黑色装备恢复 |
| 恢复倍率 | `lifeRateMul` | 恢复倍率 |
| 头部防护 | `head` | 头部防护值 |
| 头部倍率 | `headMul` | 头部防护倍率 |
| VIP头部 | `headVip` | VIP头部防护 |
| 闪避 | `dodge` | 闪避概率 |
| 格斗减伤 | `fightDedut` | 格斗伤害减免 |
| 子弹减伤 | `bulletDedut` | 子弹伤害减免 |
| 技能减伤 | `skillDedut` | 技能伤害减免 |

### 武器属性 (ARMS)
| 中文名称 | 英文属性名 | 说明 |
|---------|-----------|------|
| 弹夹容量 | `capacity` | 弹夹容量 |
| 容量倍率 | `capacityMul` | 容量倍率 |
| 黑色容量 | `capacityMulBlack` | 黑色装备容量 |
| 充能速度 | `charger` | 充能速度 |
| 充能倍率 | `chargerMul` | 充能倍率 |
| 黑色充能 | `chargerMulBlack` | 黑色装备充能 |
| 换弹速度 | `reload` | 换弹速度 |

### 移动属性 (MOTION)
| 中文名称 | 英文属性名 | 说明 |
|---------|-----------|------|
| 移动速度 | `moveMul` | 移动速度倍率 |
| 跳跃次数 | `maxJumpNumAdd` | 最大跳跃次数 |
| 前冲能量 | `fgNE` | 前冲能量消耗 |

### 特殊属性 (ONLY)
| 中文名称 | 英文属性名 | 说明 |
|---------|-----------|------|
| 伤害倍增 | `damageMul` | 伤害倍增器 |
| 冷却缩减 | `cdMul` | 技能冷却缩减 |

### 队友属性 (MORE)
| 中文名称 | 英文属性名 | 说明 |
|---------|-----------|------|
| 队友伤害 | `moreDpsMul` | 队友伤害倍率 |
| 队友生命 | `moreLifeMul` | 队友生命倍率 |
| 载具防御 | `vehicleDefMul` | 载具防御倍率 |
| 载具伤害 | `vehicleDpsMul` | 载具伤害倍率 |

## 🎁 掉落属性完整列表

### 基础掉落
| 中文名称 | 英文属性名 | 建议值 |
|---------|-----------|--------|
| 经验获取 | `exp` | +99900% |
| VIP经验 | `expVip` | +99900% |
| 银币获取 | `coinMul` | +99900% |

### 装备掉落
| 中文名称 | 英文属性名 | 建议值 |
|---------|-----------|--------|
| 武器碎片掉率 | `weaponDropPro` | +99900% |
| 装备碎片掉率 | `blackEquipDropPro` | +99900% |
| 随机武器掉率 | `ranBlackArmsDropPro` | +99900% |
| 稀有装备掉率 | `rareEquipDropPro` | +99900% |
| 稀有武器掉率 | `rareArmsDropPro` | +99900% |
| 橙色装备掉率 | `orredEquipDropPro` | +99900% |
| 橙色武器掉率 | `orredArmsDropPro` | +99900% |

### 材料掉落
| 中文名称 | 英文属性名 | 建议值 |
|---------|-----------|--------|
| 特殊零件掉率 | `specialPartsDropPro` | +99900% |
| 宝石掉率 | `gemDropPro` | +99900% |
| 尸宠图鉴掉率 | `petBookDropPro` | +99900% |
| 红橙基因体掉率 | `rareGeneDropPro` | +99900% |
| 副手掉率 | `blackArmsDropPro` | +99900% |

## 💡 实现建议

### 属性值设置建议
1. **掉落类属性**: 建议设置为 999 或 99900% (内部值为 999)
2. **数量类属性**: 建议设置为 999 个
3. **百分比属性**: 根据游戏平衡性调整，建议不超过 100%
4. **特殊属性**: 如幸运值建议设置为 999

### 添加方式
这些属性都可以通过修改装备定义文件或使用后台接口添加到装备上。所有属性都已在 `EquipPro.as` 中定义，可以直接使用。

### 注意事项
- 百分比属性在代码中通常以小数形式存储 (如 999% = 9.99)
- 某些属性有上限限制，需要注意不要超出范围
- 建议在测试环境中先验证属性效果

## 🔧 技术实现

### 属性定义位置
- **主要定义文件**: `scripts玉帝后台版/dataAll/equip/define/EquipPro.as`
- **属性显示**: `scripts玉帝后台版/dataAll/equip/creator/EquipPropertyDataCreator.as`
- **属性分类**:
  - `dpsArr`: 伤害相关属性
  - `lifeArr`: 生存相关属性
  - `dropArr`: 掉落相关属性
  - `armsOtherArr`: 武器相关属性
  - `motionArr`: 移动相关属性

### 属性添加方法

#### 方法1: 通过装备定义添加
```actionscript
// 在装备定义中添加属性
var equipObj:Object = {
    "demStroneDropNum": 999,        // 无双水晶掉落 +999个
    "demBallDropNum": 999,          // 万能球掉落 +999个
    "madheartDropNum": 999,         // 战神之心掉落 +999个
    "lottery": 999,                 // 幸运值 999
    "coinMul": 999,                 // 商运掉率 +99900%
    "arenaStampDropNum": 999,       // 优胜券获取 +999个
    "vehicleCashDropNum": 999,      // 载具碎片掉落 +999个
    "lifeCatalystDropPro": 999,     // 生命催化剂掉率 +99900%
    "godStoneDropPro": 999,         // 神能石掉率 +99900%
    "converStoneDropPro": 999,      // 转化石掉率 +99900%
    "taxStampDropPro": 999,         // 化石掉率 +99900%
    "bloodStoneDropPro": 999,       // 血手掉率 +99900%
    "deviceDropPro": 999,           // 装置掉率 +99900%
    "loveAdd": 999,                 // 赠礼好感度 +999点
    "dayLoveAdd": 999,              // 好感度每天 +999点
    "dpsAll": 0.15,                 // 战斗力/神级 15%
    "bulletDedut": 0.39,            // 防弹值 39%
    "sweepingNum": 999              // 每日扫荡次数 +999次
};
```

#### 方法2: 通过后台接口添加
可以通过修改后台刷取接口，直接为装备添加这些属性。

### 属性限制说明

#### 有上限的属性
```actionscript
// 在 EquipPro.as 中定义的上限
public static function getSumMax(name0:String) : Number
{
    if(name0 == sweepingNum) return 3 * 3;      // 扫荡次数最大9次
    if(name0 == demStroneDropNum) return 2;     // 无双水晶最大2个
    if(name0 == demBallDropNum) return 4;       // 万能球最大4个
    if(name0 == madheartDropNum) return 3;      // 战神之心最大3个
    if(name0 == dayLoveAdd) return 40;          // 每日好感度最大40点
    return 0;
}

// 防御属性上限
public static function getDodgeMax() : Number { return 0.5; }           // 闪避最大50%
public static function getFightDedutMax() : Number { return 0.65; }     // 格斗减伤最大65%
public static function getSkillDedutMax() : Number { return 0.9; }      // 技能减伤最大90%
public static function getBulletDedutMax() : Number { return 0.85; }    // 防弹值最大85%
```

#### 无上限的属性
大部分掉落类属性没有硬性上限，可以设置为999或更高值。

## 📋 完整属性代码示例

### 创建超级装备的完整属性对象
```actionscript
var superEquipProperties:Object = {
    // === 你要求的属性 ===
    "demStroneDropNum": 999,        // 无双水晶掉落 +999个
    "demBallDropNum": 999,          // 万能球掉落 +999个
    "madheartDropNum": 999,         // 战神之心掉落 +999个
    "lottery": 999,                 // 幸运值 999
    "coinMul": 999,                 // 商运掉率 +99900%
    "arenaStampDropNum": 999,       // 优胜券获取 +999个
    "vehicleCashDropNum": 999,      // 载具碎片掉落 +999个
    "lifeCatalystDropPro": 999,     // 生命催化剂掉率 +99900%
    "godStoneDropPro": 999,         // 神能石掉率 +99900%
    "converStoneDropPro": 999,      // 转化石掉率 +99900%
    "taxStampDropPro": 999,         // 化石掉率 +99900%
    "bloodStoneDropPro": 999,       // 血手掉率 +99900%
    "deviceDropPro": 999,           // 装置掉率 +99900%
    "loveAdd": 999,                 // 赠礼好感度 +999点
    "dayLoveAdd": 999,              // 好感度每天 +999点
    "dpsAll": 0.15,                 // 战斗力/神级 15%
    "bulletDedut": 0.39,            // 防弹值 39%
    "sweepingNum": 999,             // 每日扫荡次数 +999次

    // === 额外推荐属性 ===
    "exp": 999,                     // 经验获取 +99900%
    "expVip": 999,                  // VIP经验获取 +99900%
    "weaponDropPro": 999,           // 武器碎片掉率 +99900%
    "blackEquipDropPro": 999,       // 装备碎片掉率 +99900%
    "rareEquipDropPro": 999,        // 稀有装备掉率 +99900%
    "specialPartsDropPro": 999,     // 特殊零件掉率 +99900%
    "gemDropPro": 999,              // 宝石掉率 +99900%
    "petBookDropPro": 999,          // 尸宠图鉴掉率 +99900%
    "rareGeneDropPro": 999,         // 红橙基因体掉率 +99900%

    // === 战斗属性 ===
    "dps": 99999,                   // 基础伤害
    "dpsMul": 99,                   // 伤害倍率
    "life": 99999,                  // 生命值
    "lifeMul": 99,                  // 生命倍率
    "capacity": 999,                // 弹夹容量
    "charger": 999,                 // 充能速度
    "reload": 0.01                  // 换弹速度 (越小越快)
};
```

## ✅ 结论

**所有你提到的属性都可以添加到装备上！** 游戏系统完全支持这些属性，并且还有更多可用的属性可以进一步增强装备效果。

建议的实现优先级：
1. **高优先级**: 掉落类属性 (无双水晶、万能球等)
2. **中优先级**: 功能性属性 (扫荡次数、好感度等)
3. **低优先级**: 战斗属性 (需要考虑游戏平衡性)
